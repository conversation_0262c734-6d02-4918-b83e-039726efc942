## Portal Repo Tasks  portal

---

### 1. Set all requests in `ProcessPage` to approve automatically

* **Locate `ProcessPage` file:** Find the file responsible for handling request processing.
* **Identify request approval logic:** Find the section of code that handles the approval status of a request.
* **Modify approval logic:** Change the code to set the default status of all new requests to "approved" or trigger the approval action automatically upon request creation or loading within this page.
* **Remove manual approval UI (optional):** If there's a button or control for manual approval on this page, consider removing or disabling it if all requests are now auto-approved.
* **Test:** Verify that new requests in `ProcessPage` are indeed automatically approved.

### 2. In return request, move the label "download" to `CompletedState`, instead of step 4

* **Identify `CompletedState` component/logic:** Find the code that defines the "CompletedState" of a return request.
* **Locate current "download" label logic:** Find where the "download" label is currently implemented (presumably in "step 4").
* **Relocate "download" label:**
    * Remove the "download" label from its current position in "step 4".
    * Add the "download" label to the `CompletedState` UI or logic. Ensure it's displayed when a return request reaches this state.
* **Test:** Confirm the "download" label appears only when a return request is in the `CompletedState` and no longer in "step 4".

### 3. In receipt customization, users are allowed to edit once only, ask them to double confirm as it won't be able to change anymore

* **Locate receipt customization feature:** Find the files and code related to receipt editing.
* **Implement edit lock mechanism:**
    * Add a flag or status to the receipt data (e.g., `isCustomized`, `editCount`) to track if it has been edited.
    * When the user saves their first customization, set this flag.
    * Before allowing editing, check this flag. If already customized, disable editing fields or prevent access to the editing interface.
* **Add double confirmation modal/dialog:**
    * Before the first save of the customized receipt, display a confirmation message (e.g., "Are you sure you want to save these changes? You will not be able to edit the receipt again after this.").
    * Only proceed with saving if the user confirms.
* **Update UI to reflect locked state:** Clearly indicate to the user if the receipt can no longer be edited (e.g., display a message, disable edit buttons).
* **Test:**
    * Verify that a user can edit and save the receipt once.
    * Confirm the double confirmation prompt appears before the first save.
    * Ensure that after saving, the receipt customization is locked and cannot be edited again.

---

## App Repo Tasks 📱

---

### 1. In return detail page, add a scan barcode to check packaging item matches return item

* **Identify `ReturnDetailPage` component:** Find the file for the return detail page.
* **Integrate barcode scanning library/API:**
    * Choose and install a suitable barcode scanning library or use a built-in device API if available.
    * Add a button or UI element to initiate barcode scanning on the `ReturnDetailPage`.
* **Implement scanning logic:**
    * When the scan button is pressed, activate the camera for barcode scanning.
    * Capture the scanned barcode data (packaging item's identifier).
* **Implement matching logic:**
    * Fetch or access the expected return item's identifier (e.g., product SKU, barcode) from the return details.
    * Compare the scanned barcode data with the expected return item's identifier.
* **Display match/mismatch result:**
    * Show a clear visual indicator or message to the user whether the scanned packaging item matches the expected return item (e.g., "Match ✔️", "Mismatch ❌ - Expected: [Item A], Scanned: [Item B]").
* **Test:**
    * Test with matching barcodes.
    * Test with non-matching barcodes.
    * Test various lighting conditions for scanning if possible.

### 2. Create a setting page

* **Create new route/page for "Settings":** Add a new page component (e.g., `SettingsPage`) and set up routing to access it.
* **Design basic settings page UI:** Create a layout for the settings page.

    #### a. Blacklist Settings

    * **UI for blacklist:**
        * Display a list of currently blacklisted emails.
        * Provide an input field and button to manually add an email to the blacklist.
        * Provide a way to remove emails from the blacklist (e.g., a delete icon next to each email).
    * **Logic for automatic blacklisting:**
        * Access return initiation data (specifically email and frequency).
        * When a return is initiated, check the initiating email's return history.
        * If an email initiates more than 3 returns, automatically add it to the blacklist data store.
        * Ensure this check happens *before* the auto-approval logic in `ProcessPage` (from portal repo task).
    * **Data storage for blacklist:**
        * Set up a way to store the blacklist (e.g., database, local storage, state management solution).
    * **Integration with `ProcessPage` (Portal Repo):**
        * Modify the `ProcessPage` auto-approval logic (portal repo task 1) to check if the requester's email is in the blacklist.
        * If the email is blacklisted, bypass the auto-approval.
    * **Test:**
        * Manually add/remove emails from the blacklist.
        * Initiate returns with an email more than 3 times and verify it's added to the blacklist.
        * Verify that requests from blacklisted emails are not auto-approved in `ProcessPage`.

    #### b. Auto Approval Settings

    * **UI for auto-approval settings:**
        * Add an input field (e.g., number input) on the `SettingsPage` for "Number of days allowed to return a product."
        * Add a save button for this setting.
    * **Data storage for auto-approval days:**
        * Store this configuration value (e.g., in the same place as blacklist settings or a general app configuration store).
    * **Integrate with `ProcessPage` (Portal Repo):**
        * When a return request is being processed for auto-approval in `ProcessPage`, retrieve this "number of days allowed" setting.
        * Compare the product's purchase date (or a relevant date) against the current date and this setting to determine if the return is within the allowed window for auto-approval. Modify the auto-approval logic accordingly.
    * **Test:**
        * Set different values for "number of days allowed" and save.
        * Test return requests: some within the allowed days (should be considered for auto-approval if not blacklisted), some outside (should not be auto-approved based on this rule).

### 3. Add a timeline UI to address changing page

* This request is a bit vague ("timeline UI to address changing page"). Assuming it means a visual timeline for a specific process, or a history of changes. If this refers to the return process itself:
* **Identify the process to visualize:** Clarify which "changing page" or process this timeline refers to (e.g., the stages of a return request: Requested > Approved > Shipped > Received > Completed).
* **Choose/implement a timeline component:**
    * Use an existing UI library's timeline component or build a custom one.
* **Integrate timeline data:**
    * Fetch the relevant data points and their timestamps for the chosen process (e.g., dates for each stage of a return).
    * Pass this data to the timeline component.
* **Display timeline:**
    * Embed the timeline component in the relevant page (e.g., `ReturnDetailPage` to show the history of that specific return).
* **Test:**
    * Verify the timeline accurately reflects the stages and dates of the process.
    * Ensure it updates correctly as the process progresses.

If "changing page" refers to a global navigation history or audit log of page views/changes, the task would be significantly different, involving tracking navigation events or data modifications across the app. **Please clarify if the initial interpretation is incorrect.**
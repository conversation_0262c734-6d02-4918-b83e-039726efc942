"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>ert<PERSON>ircle, ImageUp, Loader2, Plus, X } from "lucide-react"
import type { Dictionary } from "@repo/internationalization"
import { Alert, AlertDescription } from "./alert"
import { FileUploader } from "./file-uploader"
import { useStorageUpload } from "../../hooks/use-storage-upload"

interface MultiPhotoUploaderProps {
  initialPhotos?: string[]
  onPhotosChange: (photos: string[]) => void
  maxPhotos?: number
  maxSizeMB?: number
  dictionary?: Dictionary
  access?: "public" | "private"
}

export function MultiPhotoUploader({
  initialPhotos = [],
  onPhotosChange,
  maxPhotos = 3,
  maxSizeMB = 5,
  dictionary,
  access = "public",
}: MultiPhotoUploaderProps) {
  const [photos, setPhotos] = useState<string[]>(initialPhotos)
  const [error, setError] = useState<string | null>(null)
  const { uploadFile, isUploading, error: upload<PERSON>rror } = useStorageUpload()

  // Sync with initialPhotos when they change externally
  useEffect(() => {
    setPhotos(initialPhotos)
  }, [initialPhotos])

  // Set error from upload if it exists
  useEffect(() => {
    if (uploadError) {
      setError(uploadError)
    }
  }, [uploadError])

  const handleAddPhoto = async (photoUrl: string | undefined) => {
    if (!photoUrl) return
    if (photos.length >= maxPhotos) {
      setError(`Maximum ${maxPhotos} photos allowed`)
      return
    }

    const updatedPhotos = [...photos, photoUrl]
    setPhotos(updatedPhotos)
    onPhotosChange(updatedPhotos)
    setError(null)
  }

  const handleFileUpload = async (file: File) => {
    if (photos.length >= maxPhotos) {
      setError(`Maximum ${maxPhotos} photos allowed`)
      return
    }

    try {
      const uploadedUrl = await uploadFile(file, { access })
      if (uploadedUrl) {
        const updatedPhotos = [...photos, uploadedUrl]
        setPhotos(updatedPhotos)
        onPhotosChange(updatedPhotos)
        setError(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to upload photo")
    }
  }

  const handleRemovePhoto = (index: number) => {
    const updatedPhotos = photos.filter((_, i) => i !== index)
    setPhotos(updatedPhotos)
    onPhotosChange(updatedPhotos)
    setError(null)
  }

  return (
    <div className="space-y-4">
      {/* Photo grid */}
      {photos.length > 0 && (
        <div className="grid grid-cols-3 gap-2">
          {photos.map((photo, index) => (
            <div
              key={`${photo}-${index}`}
              className="relative aspect-square rounded-md overflow-hidden border"
            >
              <img
                src={photo}
                alt={`Uploaded item ${index + 1}`}
                className="w-full h-full object-cover"
              />
              <button
                type="button"
                className="absolute top-1 right-1 z-10 flex h-6 w-6 items-center justify-center rounded-full bg-black/60 text-white hover:bg-black/80"
                onClick={() => handleRemovePhoto(index)}
                aria-label="Remove photo"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Add photo button or uploader */}
      {photos.length < maxPhotos ? (
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              {dictionary?.return?.add_photos || "Add Photos"}
              {photos.length > 0 && ` (${photos.length}/${maxPhotos})`}
            </span>
          </div>

          {photos.length > 0 ? (
            // Custom upload button when photos already exist
            <button
              type="button"
              className="border-input hover:bg-accent/50 flex h-12 w-full cursor-pointer items-center justify-center rounded-md border border-dashed transition-colors"
              onClick={() => {
                const input = document.createElement("input")
                input.type = "file"
                input.accept = "image/*"
                input.onchange = (e) => {
                  const target = e.target as HTMLInputElement
                  if (target.files?.[0]) {
                    const file = target.files[0]
                    handleFileUpload(file)
                  }
                }
                input.click()
              }}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span className="text-sm">{"Uploading..."}</span>
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  <span className="text-sm">{dictionary?.return?.add_photos || "Add More Photos"}</span>
                </>
              )}
            </button>
          ) : (
            // Regular FileUploader when no photos exist
            <FileUploader
              onFileChange={(fileUrl) => {
                if (!fileUrl) return

                if (typeof fileUrl === 'string') {
                  if (fileUrl.startsWith('data:')) {
                    // This is a data URL from FileReader, we need to upload it
                    const dataURLToFile = (dataUrl: string): File | null => {
                      try {
                        const arr = dataUrl.split(',')
                        const mime = arr[0].match(/:(.*?);/)?.[1]
                        const bstr = atob(arr[1])
                        let n = bstr.length
                        const u8arr = new Uint8Array(n)
                        while (n--) {
                          u8arr[n] = bstr.charCodeAt(n)
                        }
                        return new File([u8arr], `upload-${Date.now()}.${mime?.split('/')[1] || 'jpg'}`, { type: mime })
                      } catch (e) {
                        console.error('Failed to convert data URL to file:', e)
                        return null
                      }
                    }

                    const file = dataURLToFile(fileUrl)
                    if (file) {
                      handleFileUpload(file)
                    }
                  } else if (fileUrl.startsWith('blob:')) {
                    // This is a blob URL, we need to fetch it and convert to a File
                    fetch(fileUrl)
                      .then(response => response.blob())
                      .then(blob => {
                        // Create a File from the Blob
                        const file = new File([blob], `upload-${Date.now()}.${blob.type.split('/')[1] || 'jpg'}`, { type: blob.type })
                        handleFileUpload(file)
                      })
                      .catch(err => {
                        console.error('Failed to fetch blob URL:', err)
                        setError('Failed to process the image')
                      })
                  } else {
                    // This is already a URL, just add it
                    handleAddPhoto(fileUrl)
                  }
                } else if (typeof fileUrl === 'object' && fileUrl !== null) {
                  // Handle the case when fileUrl is a Blob-like object
                  try {
                    // Check if it has blob-like properties
                    if ('type' in fileUrl && 'size' in fileUrl) {
                      const blobLike = fileUrl as Blob;
                      const file = new File([blobLike], `upload-${Date.now()}.${blobLike.type.split('/')[1] || 'jpg'}`, { type: blobLike.type })
                      handleFileUpload(file)
                    } else {
                      throw new Error('Object is not a valid Blob')
                    }
                  } catch (err) {
                    console.error('Failed to process blob object:', err)
                    setError('Failed to process the image')
                  }
                } else {
                  console.error('Unsupported fileUrl type:', typeof fileUrl)
                  setError('Unsupported file type')
                }
              }}
              maxSizeMB={maxSizeMB}
              dictionary={dictionary}
            />
          )}
        </div>
      ) : (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {dictionary?.return?.max_photos_reached?.replace("{max}", String(maxPhotos)) ||
              `Maximum ${maxPhotos} photos reached`}
          </AlertDescription>
        </Alert>
      )}

      {error && (
        <div className="text-destructive flex items-center gap-1 text-xs" role="alert">
          <AlertCircle className="size-3 shrink-0" />
          <span>{error}</span>
        </div>
      )}
    </div>
  )
}

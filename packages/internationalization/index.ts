import 'server-only';
import type ja from './dictionaries/ja.json';
import languine from './languine.json';

export const locales = [
  languine.locale.source,
  ...languine.locale.targets,
] as const;

export type Dictionary = typeof ja;

const dictionaries: Record<string, () => Promise<Dictionary>> =
  Object.fromEntries(
    locales.map((locale) => [
      locale,
      () =>
        import(`./dictionaries/${locale}.json`)
          .then((mod) => mod.default)
          .catch((err) => {
            console.error(
              `Failed to load dictionary for locale: ${locale}`,
              err
            );
            return import('./dictionaries/ja.json').then((mod) => mod.default);
          }),
    ])
  );

export const getDictionary = async (locale: string): Promise<Dictionary> => {
  const normalizedLocale = locale.split('-')[0];

  if (!locales.includes(normalizedLocale)) {
    console.warn(`Locale "${locale}" is not supported, defaulting to "ja"`);
    return dictionaries.ja();
  }

  try {
    return await dictionaries[normalizedLocale]();
  } catch (error) {
    console.error(
      `Error loading dictionary for locale "${normalizedLocale}", falling back to "ja"`,
      error
    );
    return dictionaries.ja();
  }
};

{"name": "@repo/internationalization", "version": "0.0.0", "private": true, "scripts": {"translate": "npx -y languine@latest translate", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "negotiator": "^1.0.0", "next-international": "^1.3.1", "server-only": "^0.0.1"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/negotiator": "^0.6.3", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2", "next": "15.3.0"}}
{"name": "@repo/observability", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@logtail/next": "^0.2.0", "@sentry/nextjs": "^9.13.0", "@t3-oss/env-nextjs": "^0.12.0", "react": "^19.1.0", "server-only": "^0.0.1", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2", "typescript": "^5.8.3"}}
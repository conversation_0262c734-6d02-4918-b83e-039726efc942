import { PrismaClient, type User } from '@prisma/client';
import { auth } from '@repo/auth/server';

const prisma = new PrismaClient();

const users = [
  {
    email: '<EMAIL>',
    name: 'Admin',
    emailVerified: true,
    role: 'super-admin',
  },
  {
    email: '<EMAIL>',
    name: '<PERSON>',
    emailVerified: true,
    role: 'customer',
  },
  {
    email: '<EMAIL>',
    name: '<PERSON>',
    emailVerified: true,
    role: 'customer',
  },
];

async function main() {
  // Log start message without using process.stdout directly
  process.stdout.write('Starting database seed...\n');

  // Clean up existing data
  await prisma.$transaction([
    prisma.account.deleteMany(),
    prisma.session.deleteMany(),
    prisma.user.deleteMany(),
  ]);

  const usersCreated: User[] = [];

  // Create users
  for (const userData of users) {
    const user = await prisma.user.create({
      data: {
        email: userData.email,
        name: userData.name,
        emailVerified: userData.emailVerified,
        role: userData.role,
      },
    });

    // create account for password based login
    const ctx = await auth.$context;
    const hash = await ctx.password.hash('abcd1234');

    await prisma.account.create({
      data: {
        accountId: user.id,
        userId: user.id,
        providerId: 'credential',
        password: hash,
      },
    });

    usersCreated.push(user);
  }

  // Log completion message without using process.stdout directly
  process.stdout.write('Seeding complete!\n');
}

main()
  .catch((e) => {
    // Log error without using process.stderr directly
    process.stderr.write(`Error during seeding: ${e}\n`);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding CMS content...');

  // Initial CMS content
  const cmsContent = [
    {
      key: 'delivery_note',
      title: 'Delivery Note',
      description: 'Optional note displayed on delivery page',
      content: {
        en: 'Please note that delivery times may vary based on your location and current shipping conditions.',
        ja: '配送時間はお住まいの地域や現在の配送状況によって異なる場合がありますのでご了承ください。',
      },
      isPublished: true,
    },
    {
      key: 'how_to_exchange',
      title: 'How to Exchange',
      description: 'Instructions for product exchange',
      content: {
        en: `<h3>Exchange Process</h3>
<ol>
  <li>Select the items you wish to exchange</li>
  <li>Choose the reason for exchange</li>
  <li>Select the new size/color/variant</li>
  <li>Follow the instructions to return the original item</li>
  <li>We will ship your new item once we receive the return</li>
</ol>`,
        ja: `<h3>交換の手順</h3>
<ol>
  <li>交換したい商品を選択</li>
  <li>交換理由を選択</li>
  <li>新しいサイズ/カラー/バリアントを選択</li>
  <li>元の商品を返品するための指示に従ってください</li>
  <li>返品を受け取り次第、新しい商品を発送します</li>
</ol>`,
      },
      isPublished: true,
    },
    {
      key: 'how_to_return',
      title: 'How to Return',
      description: 'Instructions for product return',
      content: {
        en: `<h3>Return Process</h3>
<ol>
  <li>Select the items you wish to return</li>
  <li>Choose the reason for return</li>
  <li>Print the return label or request a return envelope</li>
  <li>Package your items securely</li>
  <li>Drop off the package at a shipping location</li>
  <li>Refund will be processed once we receive and inspect the return</li>
</ol>`,
        ja: `<h3>返品の手順</h3>
<ol>
  <li>返品したい商品を選択</li>
  <li>返品理由を選択</li>
  <li>返品ラベルを印刷するか、返品用封筒をリクエスト</li>
  <li>商品を安全に梱包</li>
  <li>配送場所にパッケージを持ち込む</li>
  <li>返品を受け取り検査した後、返金が処理されます</li>
</ol>`,
      },
      isPublished: true,
    },
    {
      key: 'about_refund',
      title: 'About Refund',
      description: 'Information about refund policy',
      content: {
        en: `<h3>Refund Policy</h3>
<p>Refunds are typically processed within 5-7 business days after we receive your return. The refund will be issued to the original payment method.</p>
<p>Please note that shipping costs are non-refundable, and a restocking fee may apply for certain items.</p>`,
        ja: `<h3>返金ポリシー</h3>
<p>返金は通常、返品を受け取ってから5〜7営業日以内に処理されます。返金は元のお支払い方法に対して行われます。</p>
<p>配送料は返金対象外であり、一部の商品には再在庫手数料が適用される場合がありますのでご注意ください。</p>`,
      },
      isPublished: true,
    },
  ];

  // Upsert CMS content (create if not exists, update if exists)
  for (const content of cmsContent) {
    await prisma.cmsContent.upsert({
      where: { key: content.key },
      update: content,
      create: content,
    });
  }

  console.log('CMS content seeded successfully!');
}

main()
  .catch((e) => {
    console.error('Error seeding CMS content:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

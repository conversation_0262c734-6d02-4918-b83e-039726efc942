"use client"

import React, { useEffect, useRef } from "react"
import { AlertCircle, ImageUp, X } from "lucide-react"
import type { Dictionary } from "@repo/internationalization"
import { useFileUpload } from "@repo/design-system/hooks/use-file-upload"

interface FileUploaderProps {
  initialPreview?: string
  onFileChange: (fileUrl: string | undefined) => void
  maxSizeMB?: number
  dictionary?: Dictionary
}

export function FileUploader({
  initialPreview,
  onFileChange,
  maxSizeMB = 5,
  dictionary,
}: FileUploaderProps) {
  const maxSize = maxSizeMB * 1024 * 1024 // Convert MB to bytes

  // Create initial files metadata if initialPreview is provided
  const initialFiles = initialPreview
    ? [
        {
          name: "initial-preview.jpg",
          size: 0,
          type: "image/jpeg",
          url: initialPreview,
          id: "initial-preview",
        },
      ]
    : []

  // Initialize the ref with initialPreview to avoid triggering the effect on first render
  const initialRender = useRef(true)

  const [
    { files, isDragging, errors },
    {
      handleDragEnter,
      handleDragLeave,
      handleDragOver,
      handleDrop,
      openFileDialog,
      removeFile,
      getInputProps,
    },
  ] = useFileUpload({
    accept: "image/*",
    maxSize,
    initialFiles,
  })

  // Get the preview URL from the files array
  const previewUrl = files[0]?.preview || null

  // Use a ref to track the previous file URL to avoid infinite loops
  const prevFileUrlRef = useRef<string | null>(null)

  // Update parent component when files change, but only when the file URL actually changes
  useEffect(() => {
    // Skip the first render if we have initialPreview
    if (initialRender.current && initialPreview) {
      initialRender.current = false
      prevFileUrlRef.current = initialPreview
      return
    }

    const currentUrl = files[0]?.preview || null

    // Only update if the URL has changed
    if (currentUrl !== prevFileUrlRef.current) {
      prevFileUrlRef.current = currentUrl

      if (currentUrl) {
        onFileChange(currentUrl)
      }
      // Note: We don't call onFileChange(undefined) here
      // That's handled by the remove button click handler
    }
    // We intentionally don't include prevFileUrlRef in the dependencies
    // as it would cause the effect to run again unnecessarily
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [files, onFileChange, initialPreview])

  return (
    <div className="flex flex-col gap-2">
      <div className="relative">
        {/* Drop area */}
        {/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
        {/* biome-ignore lint/a11y/useFocusableInteractive: <explanation> */}
        {/* biome-ignore lint/a11y/useSemanticElements: <explanation> */}
        <div
          role="button"
          onClick={openFileDialog}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          data-dragging={isDragging || undefined}
          className="border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 relative flex min-h-40 flex-col items-center justify-center overflow-hidden rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[img]:border-none has-[input:focus]:ring-[3px]"
        >
          <input {...getInputProps()} className="sr-only" aria-label="Upload file" />
          {previewUrl ? (
            <div className="absolute inset-0">
              <img src={previewUrl} alt="Uploads" className="size-full object-cover" />
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center px-4 py-3 text-center">
              <div
                className="bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border"
                aria-hidden="true"
              >
                <ImageUp className="size-4 opacity-60" />
              </div>
              <p className="mb-1.5 text-sm font-medium">
                {dictionary?.common?.drop_image || "Drop your image here or click to browse"}
              </p>
              <p className="text-muted-foreground text-xs">
                {dictionary?.common?.max_size?.replace("{maxSize}", String(maxSizeMB)) ||
                  `Max size: ${maxSizeMB}MB`}
              </p>
            </div>
          )}
        </div>
        {previewUrl && (
          <div className="absolute top-4 right-4">
            <button
              type="button"
              className="focus-visible:border-ring focus-visible:ring-ring/50 z-50 flex size-8 cursor-pointer items-center justify-center rounded-full bg-black/60 text-white transition-[color,box-shadow] outline-none hover:bg-black/80 focus-visible:ring-[3px]"
              onClick={() => {
                // First update the ref to avoid triggering the useEffect
                prevFileUrlRef.current = null

                // Then remove the file if it exists
                if (files.length > 0) {
                  removeFile(files[0]?.id)
                }

                // Finally call onFileChange with undefined
                onFileChange(undefined)
              }}
              aria-label="Remove image"
            >
              <X className="size-4" aria-hidden="true" />
            </button>
          </div>
        )}
      </div>

      {errors.length > 0 && (
        <div className="text-destructive flex items-center gap-1 text-xs" role="alert">
          <AlertCircle className="size-3 shrink-0" />
          <span>{errors[0]}</span>
        </div>
      )}
    </div>
  )
}

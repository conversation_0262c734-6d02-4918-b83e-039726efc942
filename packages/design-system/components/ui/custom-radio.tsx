"use client"

import { cn } from "@repo/design-system/lib/utils"
import type * as React from "react"

interface CustomRadioProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string
  checked: boolean
  onCheckedChange: (checked: boolean) => void
  className?: string
}

export function CustomRadio({
  label,
  checked,
  onCheckedChange,
  className,
  id,
  ...props
}: CustomRadioProps) {
  return (
    <label
      htmlFor={id}
      className={cn(
        "border-input relative flex cursor-pointer flex-col gap-4 border p-4 outline-none first:rounded-t-md last:rounded-b-md hover:bg-accent/50",
        checked ? "border-primary/50 bg-accent z-10" : "[&:not(:first-child)]:border-t-transparent",
        className
      )}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 cursor-pointer">
          <div className="relative flex h-4 w-4 items-center justify-center rounded-full border border-input shadow-xs">
            {checked && <div className="h-2 w-2 rounded-full bg-primary" />}
          </div>
          <input
            type="radio"
            id={id}
            checked={checked}
            onChange={() => onCheckedChange(!checked)}
            className="sr-only"
            {...props}
          />
          <span className="inline-flex items-start">{label}</span>
        </div>
      </div>
    </label>
  )
}

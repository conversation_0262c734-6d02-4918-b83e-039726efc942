import { format as dateFnsFormat } from "date-fns"

export function dateFormatter(date: Date, format: string): string {
  return dateFnsFormat(date, format)
}

// Format time for display
export const formatTime = (time: string | Date) => {
  return dateFnsFormat(new Date(time), "h:mm a")
}

// Format date for display
export const formatDate = (date: string | Date) => {
  return dateFnsFormat(new Date(date), "MMM d, yyyy")
}

// Format date & time for display
export const formatDateTime = (date: string | Date) => {
  return dateFnsFormat(new Date(date), "MMM d, yyyy h:mm a")
}

/**
 * Format a currency amount according to the current locale
 * @param amount - The amount to format
 * @param locale - The locale to use for formatting (defaults to browser locale)
 * @param currency - The currency code to use (defaults to USD)
 */
export function formatCurrency(
  amount: string | number,
  locale?: string,
  currency?: string
): string {
  const numericAmount = typeof amount === "string" ? Number.parseFloat(amount) : amount

  return new Intl.NumberFormat(locale || "en-US", {
    style: "currency",
    currency: currency || "USD",
  }).format(numericAmount)
}

"use client"

import { useState } from "react"

interface UploadOptions {
  access?: "public" | "private"
}

interface UploadResult {
  url: string
  pathname: string
}

/**
 * Hook for uploading files to the storage service
 */
export function useStorageUpload() {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Upload a file to the storage service
   * @param file The file to upload
   * @param options Upload options
   * @returns The URL of the uploaded file
   */
  const uploadFile = async (
    file: File,
    options: UploadOptions = {}
  ): Promise<string | null> => {
    setIsUploading(true)
    setError(null)

    try {
      const formData = new FormData()
      formData.append("file", file)

      if (options.access) {
        formData.append("access", options.access)
      }

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to upload file")
      }

      const data: UploadResult = await response.json()
      return data.url
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to upload file")
      return null
    } finally {
      setIsUploading(false)
    }
  }

  return {
    uploadFile,
    isUploading,
    error,
  }
}

import { adminClient, magicLinkClient } from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import { superAdminRole } from './permissions';
import { ac } from './permissions';

// type AuthClient = ReturnType<
//   typeof createAuthClient<{
//     plugins: [
//       ReturnType<typeof adminClient>,
//       ReturnType<typeof magicLinkClient>,
//       // ReturnType<typeof organizationClient>,
//     ];
//   }>
// >;

const authClient = createAuthClient({
  plugins: [
    // customSessionClient<typeof auth>(),
    adminClient({
      ac,
      roles: {
        superAdmin: superAdminRole,
      },
    }),

    magicLinkClient(),
  ],
});

export const { signIn, signOut, signUp, useSession, admin } = authClient;
export default authClient;

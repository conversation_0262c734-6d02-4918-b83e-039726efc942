import { createAccessControl } from 'better-auth/plugins/access';
import {
  adminAc,
  defaultStatements as adminDefaultStatements,
} from 'better-auth/plugins/admin/access';

const statement = {
  ...adminDefaultStatements, // user, session
} as const;

export const ac = createAccessControl(statement);

// Admin role - has full access to everything
export const superAdminRole = ac.newRole({
  ...adminAc.statements,
});

export const adminRole = ac.newRole({});

// Customer role -
export const customerRole = ac.newRole({});

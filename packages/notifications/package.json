{"name": "@repo/notifications", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@knocklabs/node": "^0.6.19", "@knocklabs/react": "^0.7.3", "@t3-oss/env-nextjs": "^0.12.0", "react": "^19.1.0", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5.8.3", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2"}}
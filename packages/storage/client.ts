import { S3Client } from '@aws-sdk/client-s3';
import { keys } from './keys';

// Initialize the S3 client
export const s3Client = new S3Client({
  endpoint: `https://${keys().SPACES_REGION}.digitaloceanspaces.com`,
  region: keys().SPACES_REGION,
  credentials: {
    accessKeyId: keys().SPACES_KEY,
    secretAccessKey: keys().SPACES_SECRET,
  },
});

// Re-export getSignedUrl from the AWS SDK
export { getSignedUrl } from '@aws-sdk/s3-request-presigner';

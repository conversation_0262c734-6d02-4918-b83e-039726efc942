import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const keys = () =>
  createEnv({
    server: {
      SPACES_REGION: z.string().min(1),
      SPACES_KEY: z.string().min(1),
      SPACES_SECRET: z.string().min(1),
      SPACES_BUCKET_NAME: z.string().min(1),
    },
    runtimeEnv: {
      SPACES_REGION: process.env.SPACES_REGION,
      SPACES_KEY: process.env.SPACES_KEY,
      SPACES_SECRET: process.env.SPACES_SECRET,
      SPACES_BUCKET_NAME: process.env.SPACES_BUCKET_NAME,
    },
  });

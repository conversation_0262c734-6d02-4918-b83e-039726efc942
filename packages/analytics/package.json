{"name": "@repo/analytics", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@next/third-parties": "15.3.0", "@t3-oss/env-nextjs": "^0.12.0", "@vercel/analytics": "^1.5.0", "posthog-js": "^1.236.1", "posthog-node": "^4.11.7", "react": "^19.1.0", "server-only": "^0.0.1", "zod": "^3.24.3"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.14.1", "@types/react": "19.1.2", "@types/react-dom": "^19.1.2"}}
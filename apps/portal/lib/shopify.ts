import { env } from '@/env';
import type { GetOrdersQuery } from '@/types/admin.generated';
import { log } from '@repo/observability/log';
import { createAdminApiClient } from '@shopify/admin-api-client';

// Define the interface for the address input
export interface MailingAddressInput {
  address1: string;
  address2?: string;
  city: string;
  province: string;
  zip: string;
  country: string;
  firstName?: string;
  lastName?: string;
  company?: string;
  phone?: string;
}

/**
 * Function to fetch basic order information using the Admin API Client (GraphQL)
 * This is a lightweight version that only retrieves minimal information to check if an order exists
 * Use this function for quick verification of order existence and email matching
 */
export async function getOrderBasicInfo(orderName: string, email?: string) {
  const shopDomain = env.SHOPIFY_SHOP_DOMAIN;
  // Ensure you are using an Admin API access token (offline preferred)
  const accessToken = env.SHOPIFY_API_ACCESS_TOKEN;

  if (!shopDomain || !accessToken) {
    log.error(
      'Shopify domain or access token is missing in environment variables.'
    );
    return null;
  }

  const client = createAdminApiClient({
    storeDomain: shopDomain,
    apiVersion: '2025-04',
    accessToken: accessToken,
  });

  // Simplified GraphQL Query to fetch minimal order details
  const query = `#graphql
        query GetOrderBasicInfo($query: String!) {
            orders(first: 1, query: $query) {
                edges {
                    node {
                        id
                        name
                        email
                        statusPageUrl
                        shippingAddress {
                            firstName
                            lastName
                            address1
                            address2
                            city
                            province
                            zip
                            country
                            phone
                            company
                        }
                    }
                }
            }
        }
    ` as const;

  try {
    log.info(`Fetching basic order info for ${orderName} from ${shopDomain}`);
    const response = await client.request(query, {
      variables: { query: `name:${orderName}` },
    });

    // Check for GraphQL errors
    if (response.errors) {
      log.error(`GraphQL errors fetching order ${orderName}:`, {
        error: response.errors,
      });
      return null;
    }

    if ((response.data?.orders?.edges?.length ?? 0) > 0) {
      const orderData = response.data?.orders?.edges[0]?.node;
      log.info(`Successfully fetched basic info for order: ${orderData?.name}`);

      // If email is provided, verify it matches the order email
      if (email) {
        const orderEmail = orderData?.email;
        if (!orderEmail || orderEmail.toLowerCase() !== email.toLowerCase()) {
          log.error(
            `Email verification failed for order ${orderName}. Email doesn't match.`
          );
          return null; // Return null if email doesn't match
        }
        log.info(`Email verification successful for order ${orderName}`);
      }

      return orderData;
    }

    log.error(
      `Order with name ${orderName} not found or empty data in response.`
    );
    return null;
  } catch (error) {
    log.error(
      `Error fetching basic Shopify order info with name ${orderName}:`,
      { error }
    );
    return null;
  }
}

/**
 * Function to fetch complete order details using the Admin API Client (GraphQL)
 * This version retrieves all the information needed for the receipt and other detailed views
 * Use this function when you need comprehensive order information
 */
export async function getOrderByName(orderName: string, email?: string) {
  const shopDomain = env.SHOPIFY_SHOP_DOMAIN;
  // Ensure you are using an Admin API access token (offline preferred)
  const accessToken = env.SHOPIFY_API_ACCESS_TOKEN;

  if (!shopDomain || !accessToken) {
    log.error(
      'Shopify domain or access token is missing in environment variables.'
    );
    return null;
  }

  const client = createAdminApiClient({
    storeDomain: shopDomain,
    apiVersion: '2025-04',
    accessToken: accessToken,
  });

  // GraphQL Query to fetch complete order details
  // Includes all fields needed for the receipt and other detailed views
  const query = `#graphql
        query GetOrders($query: String!) {
            orders(first: 1, query: $query) {
                edges {
                    node {
                        id # This is the GID (e.g., gid://shopify/Order/...)
                        legacyResourceId # This is the numeric ID you passed in
                        name # This is the order number (e.g., #1001)
                        statusPageUrl
                        createdAt
                        processedAt
                        email
                        taxesIncluded
                        totalPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        subtotalPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalShippingPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalTaxSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        totalDiscountsSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        currentTotalPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        currentSubtotalPriceSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        currentTotalTaxSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        currentTotalDiscountsSet {
                            shopMoney {
                                amount
                                currencyCode
                            }
                        }
                        shippingAddress {
                            firstName
                            lastName
                            address1
                            address2
                            city
                            province
                            zip
                            country
                            phone
                            company
                        }
                        billingAddress {
                            firstName
                            lastName
                            address1
                            address2
                            city
                            province
                            zip
                            country
                            phone
                            company
                        }
                        lineItems(first: 25) {
                            edges {
                                node {
                                    id
                                    name # Combination of product title and variant title
                                    title # Product title
                                    variantTitle
                                    quantity
                                    sku
                                    originalUnitPriceSet {
                                        shopMoney {
                                            amount
                                            currencyCode
                                        }
                                    }
                                    discountedTotalSet {
                                        shopMoney {
                                            amount
                                            currencyCode
                                        }
                                    }
                                    totalDiscountSet {
                                        shopMoney {
                                            amount
                                            currencyCode
                                        }
                                    }
                                    image {
                                        url
                                    }
                                    taxLines {
                                        priceSet {
                                            shopMoney {
                                                amount
                                                currencyCode
                                            }
                                        }
                                        rate
                                        title
                                    }
                                }
                            }
                        }
                        taxLines {
                            priceSet {
                                shopMoney {
                                    amount
                                    currencyCode
                                }
                            }
                            rate
                            title
                        }
                        paymentGatewayNames
                        fulfillments(first: 10) {
                            id
                            status
                            createdAt
                            updatedAt
                            deliveredAt
                            estimatedDeliveryAt
                            inTransitAt
                            trackingInfo {
                                company
                                number
                                url
                            }
                            events(first: 10) {
                                edges {
                                    node {
                                        id
                                        status
                                        happenedAt
                                        city
                                        province
                                        country
                                        message
                                    }
                                }
                            }
                        }
                        shippingLine {
                            title
                            originalPriceSet {
                                shopMoney {
                                    amount
                                    currencyCode
                                }
                            }
                        }
                        note
                        customAttributes {
                            key
                            value
                        }
                    }
                }
            }
        }
    ` as const;

  try {
    log.info(
      `Fetching complete order details for ${orderName} from ${shopDomain}`
    );
    const response = await client.request<GetOrdersQuery>(query, {
      variables: { query: `name:${orderName}` },
    });

    // Check for GraphQL errors
    if (response.errors) {
      log.error(`GraphQL errors fetching order ${orderName}:`, {
        error: response.errors,
      });
      return null;
    }

    if ((response.data?.orders?.edges?.length ?? 0) > 0) {
      const orderData = response.data?.orders?.edges[0]?.node;
      log.info(
        `Successfully fetched complete details for order GID: ${orderData?.id}`
      );

      // If email is provided, verify it matches the order email
      if (email) {
        const orderEmail = orderData?.email;
        if (!orderEmail || orderEmail.toLowerCase() !== email.toLowerCase()) {
          log.error(
            `Email verification failed for order ${orderName}. Email doesn't match.`
          );
          return null; // Return null if email doesn't match
        }
        log.info(`Email verification successful for order ${orderName}`);
      }

      return orderData;
    }

    log.error(
      `Order with name ${orderName} not found or empty data in response.`
    );
    return null;
  } catch (error) {
    // Handle network errors or other issues with the request itself
    log.error(
      `Error fetching complete Shopify order details with name ${orderName}:`,
      { error }
    );
    return null;
  }
}

/**
 * Verifies if the user is allowed to access the order details
 * Always requires email verification for security
 * Uses the lightweight getOrderBasicInfo function for better performance
 */
export async function verifyOrderAccess(orderName: string, email?: string) {
  // If no email is provided, we can't verify the user
  if (!email) {
    return null;
  }

  // If email is provided, verify it matches the order using the lightweight function
  return await getOrderBasicInfo(orderName, email);
}

/**
 * Updates the shipping address for an order
 * @param orderId The Shopify order ID (gid://shopify/Order/...)
 * @param address The new shipping address
 * @returns The updated order or null if there was an error
 */
export async function updateOrderShippingAddress(
  orderId: string,
  address: MailingAddressInput
) {
  const shopDomain = env.SHOPIFY_SHOP_DOMAIN;
  const accessToken = env.SHOPIFY_API_ACCESS_TOKEN;

  if (!shopDomain || !accessToken) {
    log.error(
      'Shopify domain or access token is missing in environment variables.'
    );
    return { success: false, error: 'Configuration error' };
  }

  const client = createAdminApiClient({
    storeDomain: shopDomain,
    apiVersion: '2025-04',
    accessToken: accessToken,
  });

  // GraphQL mutation to update the order's shipping address
  const mutation = `#graphql
        mutation UpdateOrderShippingAddress($id: ID!, $address: MailingAddressInput!) {
          orderUpdate(input: {
            id: $id,
            shippingAddress: $address
          }) {
            order {
              id
              shippingAddress {
                firstName
                lastName
                address1
                address2
                city
                province
                zip
                country
                phone
                company
              }
            }
            userErrors {
              field
              message
            }
          }
        }
    `;

  try {
    log.info(`Updating shipping address for order ${orderId}`);
    const response = await client.request(mutation, {
      variables: {
        id: orderId,
        address: address,
      },
    });

    // Check for GraphQL errors
    if (response.errors) {
      log.error(`GraphQL errors updating order ${orderId}:`, {
        error: response.errors,
      });
      return {
        success: false,
        error: 'GraphQL error',
        details: response.errors,
      };
    }

    // Check for user errors
    if ((response.data?.orderUpdate?.userErrors?.length ?? 0) > 0) {
      log.error(`User errors updating order ${orderId}:`, {
        error: response.data?.orderUpdate?.userErrors,
      });
      return {
        success: false,
        error: 'Validation error',
        details: response.data?.orderUpdate?.userErrors,
      };
    }

    log.info(`Successfully updated shipping address for order ${orderId}`);
    return {
      success: true,
      order: response.data?.orderUpdate?.order,
    };
  } catch (error) {
    log.error(`Error updating shipping address for order ${orderId}:`, {
      error,
    });
    return {
      success: false,
      error: 'Request error',
      details: error,
    };
  }
}

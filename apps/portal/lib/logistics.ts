import { env } from '@/env';

export function getLogisticsStatus(orderName: string) {
  return fetch(
    `https://orders-api.casefinite.jp/api/v1/admin/orders?name=${orderName}`,
    {
      headers: {
        'access-secret-token': env.CASEFINITE_API_KEY,
      },
    }
  )
    .then((res) => res.json())
    .then((data) => {
      return data.data.at(0).order_status_id >= 4; // already start packaging
    })
    .catch((err) => {
      console.error(err);
    });
}

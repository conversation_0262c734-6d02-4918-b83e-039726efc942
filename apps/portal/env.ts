import { keys as flags } from '@repo/feature-flags/keys';
import { keys as core } from '@repo/next-config/keys';
import { keys as observability } from '@repo/observability/keys';
import { keys as rateLimit } from '@repo/rate-limit/keys';
import { keys as security } from '@repo/security/keys';
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  extends: [core(), observability(), flags(), security(), rateLimit()],
  server: {
    SHOPIFY_SHOP_DOMAIN: z.string().min(1),
    SHOPIFY_API_ACCESS_TOKEN: z.string().min(1),
    CASEFINITE_API_KEY: z.string().min(1),
  },
  client: {
    NEXT_PUBLIC_SITE_TITLE: z.string().min(1),
    NEXT_PUBLIC_SHOPIFY_SHOP_DOMAIN: z.string().min(1),
    NEXT_PUBLIC_REGION: z.enum(['JP', 'GLOBAL']).optional(),
  },
  runtimeEnv: {
    SHOPIFY_SHOP_DOMAIN: process.env.SHOPIFY_SHOP_DOMAIN,
    SHOPIFY_API_ACCESS_TOKEN: process.env.SHOPIFY_API_ACCESS_TOKEN,
    NEXT_PUBLIC_SITE_TITLE: process.env.NEXT_PUBLIC_SITE_TITLE,
    NEXT_PUBLIC_SHOPIFY_SHOP_DOMAIN:
      process.env.NEXT_PUBLIC_SHOPIFY_SHOP_DOMAIN,
    NEXT_PUBLIC_REGION: process.env.NEXT_PUBLIC_REGION,
    CASEFINITE_API_KEY: process.env.CASEFINITE_API_KEY,
  },
});

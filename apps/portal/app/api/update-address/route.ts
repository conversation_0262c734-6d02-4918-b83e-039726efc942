import { createAddressChangeRequest, createAdminNotification } from '@/lib/db';
import {
  type MailingAddressInput,
  updateOrderShippingAddress,
  verifyOrderAccess,
} from '@/lib/shopify';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const {
      orderName,
      email,
      address,
    }: {
      orderName: string;
      email: string;
      address: MailingAddressInput;
    } = await request.json();

    log.info(`Updating address for order ${orderName} with email ${email}`);

    if (!orderName || !email || !address) {
      return NextResponse.json(
        { error: 'Order name, email, and address are required' },
        { status: 400 }
      );
    }

    // Verify the order with email
    const order = await verifyOrderAccess(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          message:
            "Order not found or email doesn't match. Please check your information and try again.",
        },
        { status: 200 }
      );
    }

    // Store the original address for record-keeping
    const originalAddress = order.shippingAddress || {};

    // Save the address change request to the database
    await createAddressChangeRequest({
      orderName,
      email,
      originalAddress: JSON.parse(JSON.stringify(originalAddress)),
      newAddress: JSON.parse(JSON.stringify(address)),
      status: 'pending',
    });

    // Create admin notification
    await createAdminNotification({
      type: 'address_change',
      message: `Address change request for order ${orderName}`,
      relatedOrderName: orderName,
    });

    // Update the shipping address in Shopify
    const result = await updateOrderShippingAddress(order.id, address);

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          message: 'Failed to update shipping address',
          error: result.error,
          details: result.details,
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'Shipping address updated successfully',
        order: result.order,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error updating address:', { error });
    return NextResponse.json(
      { error: 'An error occurred while updating the address' },
      { status: 500 }
    );
  }
}

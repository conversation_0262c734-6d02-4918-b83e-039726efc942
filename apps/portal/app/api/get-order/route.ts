import { getOrderByName } from '@/lib/shopify';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { orderName, email }: { orderName: string; email?: string } =
      await request.json();

    log.info(`Fetching order details for ${orderName}`);

    if (!orderName) {
      return NextResponse.json(
        { error: 'Order number is required' },
        { status: 400 }
      );
    }

    // Fetch the order details using the server function
    const order = await getOrderByName(orderName, email);

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          message:
            'Order not found. Please check your information and try again.',
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        order: order,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error fetching order details:', { error });
    return NextResponse.json(
      { error: 'An error occurred while fetching the order details' },
      { status: 500 }
    );
  }
}

import { updateReceiptDownloadCount } from '@/lib/db';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { id } = await request.json();

    log.info(`Updating receipt download count for ID ${id}`);

    if (!id) {
      return NextResponse.json(
        { error: 'Receipt customization ID is required' },
        { status: 400 }
      );
    }

    // Update the download count
    const updatedCustomization = await updateReceiptDownloadCount(id);

    return NextResponse.json(
      {
        success: true,
        message: 'Receipt download count updated successfully',
        customization: updatedCustomization,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error updating receipt download count:', { error });
    return NextResponse.json(
      { error: 'An error occurred while updating the receipt download count' },
      { status: 500 }
    );
  }
}

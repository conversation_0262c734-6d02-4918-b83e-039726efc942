import { prisma } from '@/lib/prisma';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    let read: boolean | undefined;
    if (searchParams.get('read') === 'true') {
      read = true;
    } else if (searchParams.get('read') === 'false') {
      read = false;
    }
    const page = Number.parseInt(searchParams.get('page') || '1');
    const limit = Number.parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    // Build the query
    const where = read !== undefined ? { read } : {};

    // Get the total count
    const total = await prisma.adminNotification.count({ where });

    // Get the notifications
    const notifications = await prisma.adminNotification.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    return NextResponse.json({
      success: true,
      data: notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    log.error('Error fetching notifications:', { error });
    return NextResponse.json(
      { error: 'An error occurred while fetching notifications' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, read } = await request.json();

    if (!id || read === undefined) {
      return NextResponse.json(
        { error: 'Notification ID and read status are required' },
        { status: 400 }
      );
    }

    // Update the notification
    const updatedNotification = await prisma.adminNotification.update({
      where: { id },
      data: {
        read,
        readAt: read ? new Date() : null,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedNotification,
    });
  } catch (error) {
    log.error('Error updating notification:', { error });
    return NextResponse.json(
      { error: 'An error occurred while updating the notification' },
      { status: 500 }
    );
  }
}

// Mark all notifications as read
export async function PATCH(_request: NextRequest) {
  try {
    // Update all unread notifications
    const result = await prisma.adminNotification.updateMany({
      where: { read: false },
      data: {
        read: true,
        readAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      count: result.count,
    });
  } catch (error) {
    log.error('Error marking all notifications as read:', { error });
    return NextResponse.json(
      { error: 'An error occurred while marking all notifications as read' },
      { status: 500 }
    );
  }
}

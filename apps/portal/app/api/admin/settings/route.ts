import { prisma } from '@/lib/prisma';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const region = searchParams.get('region') || undefined;

    // Get the feature settings
    const settings = await prisma.featureSettings.findMany({
      where: region ? { region } : {},
      orderBy: [{ region: 'asc' }, { name: 'asc' }],
    });

    return NextResponse.json({
      success: true,
      data: settings,
    });
  } catch (error) {
    log.error('Error fetching feature settings:', { error });

    return NextResponse.json(
      { error: 'An error occurred while fetching feature settings' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, value, enabled } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Feature setting ID is required' },
        { status: 400 }
      );
    }

    // Update the feature setting
    const updateData: { value?: string; enabled?: boolean } = {};
    if (value !== undefined) {
      updateData.value = value;
    }
    if (enabled !== undefined) {
      updateData.enabled = enabled;
    }

    const updatedSetting = await prisma.featureSettings.update({
      where: { id },
      data: updateData,
    });

    return NextResponse.json({
      success: true,
      data: updatedSetting,
    });
  } catch (error) {
    log.error('Error updating feature setting:', { error });
    return NextResponse.json(
      { error: 'An error occurred while updating the feature setting' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { name, value, description, region, enabled } = await request.json();

    if (!name || value === undefined) {
      return NextResponse.json(
        { error: 'Feature setting name and value are required' },
        { status: 400 }
      );
    }

    // Create a new feature setting
    const newSetting = await prisma.featureSettings.create({
      data: {
        name,
        value,
        description,
        region,
        enabled: enabled !== undefined ? enabled : true,
      },
    });

    return NextResponse.json({
      success: true,
      data: newSetting,
    });
  } catch (error) {
    log.error('Error creating feature setting:', { error });
    return NextResponse.json(
      { error: 'An error occurred while creating the feature setting' },
      { status: 500 }
    );
  }
}

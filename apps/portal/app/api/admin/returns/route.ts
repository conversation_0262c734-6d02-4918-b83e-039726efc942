import { prisma } from '@/lib/prisma';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') || undefined;
    const page = Number.parseInt(searchParams.get('page') || '1');
    const limit = Number.parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the query
    const where = status ? { status } : {};

    // Get the total count
    const total = await prisma.returnRequest.count({ where });

    // Get the return requests
    const returnRequests = await prisma.returnRequest.findMany({
      where,
      include: {
        returnItems: true,
        defectPhotos: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    return NextResponse.json({
      success: true,
      data: returnRequests,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    log.error('Error fetching return requests:', { error });
    return NextResponse.json(
      { error: 'An error occurred while fetching return requests' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, status } = await request.json();

    if (!id || !status) {
      return NextResponse.json(
        { error: 'Return request ID and status are required' },
        { status: 400 }
      );
    }

    // Update the return request status
    const updatedRequest = await prisma.returnRequest.update({
      where: { id },
      data: { status },
      include: {
        returnItems: true,
        defectPhotos: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedRequest,
    });
  } catch (error) {
    log.error('Error updating return request:', { error });
    return NextResponse.json(
      { error: 'An error occurred while updating the return request' },
      { status: 500 }
    );
  }
}

import { prisma } from '@/lib/prisma';
import { log } from '@repo/observability/log';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { orderName, email } = await request.json();

    log.info(`Fetching receipt customization for order ${orderName}`);

    if (!orderName || !email) {
      return NextResponse.json(
        { error: 'Order name and email are required' },
        { status: 400 }
      );
    }

    // Find the receipt customization
    const customization = await prisma.receiptCustomization.findUnique({
      where: {
        orderName_email: {
          orderName,
          email,
        },
      },
    });

    if (!customization) {
      return NextResponse.json(
        {
          success: false,
          message: 'No receipt customization found for this order',
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        customization,
      },
      { status: 200 }
    );
  } catch (error) {
    log.error('Error fetching receipt customization:', { error });
    return NextResponse.json(
      { error: 'An error occurred while fetching the receipt customization' },
      { status: 500 }
    );
  }
}

import { log } from '@repo/observability/log';
import { put } from '@repo/storage';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    log.info('Upload request received');

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      log.error('No file found in request');
      return NextResponse.json({ error: 'File is required' }, { status: 400 });
    }

    const access = (formData.get('access') as 'public' | 'private') || 'public';

    log.info('Uploading file', {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      access,
    });

    // Validate file
    if (!file.type.startsWith('image/')) {
      log.error('Invalid file type', { fileType: file.type });
      return NextResponse.json(
        { error: 'Only image files are allowed' },
        { status: 400 }
      );
    }

    const result = await put(file.name, file, {
      access,
      contentType: file.type,
    });

    log.info('File uploaded successfully', {
      url: result.url,
      pathname: result.pathname,
    });

    return NextResponse.json({
      success: true,
      url: result.url,
      pathname: result.pathname,
    });
  } catch (error) {
    log.error('Failed to upload file:', { error });

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

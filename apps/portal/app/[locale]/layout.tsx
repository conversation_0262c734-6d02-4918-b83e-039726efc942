import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import type { ReactNode } from 'react';
import '../globals.css';
import { LanguageSwitcher } from '@/components/language-switcher';
import { env } from '@/env';
import { getDictionary, locales } from '@repo/internationalization';
import Link from 'next/link';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;

  const dictionary = await getDictionary(locale); // Load dictionary for potential future use

  return {
    title: env.NEXT_PUBLIC_SITE_TITLE ?? 'Return & Exchange Portal',
    description: dictionary.site.description,
  };
}

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params,
}: Readonly<{
  readonly children: ReactNode;
  readonly params: Promise<{
    locale: string;
  }>;
}>) {
  const { locale } = await params;

  // Import the dictionary directly based on locale
  const dictionary = await getDictionary(locale);

  return (
    <html lang={locale ?? 'ja'}>
      <meta charSet="utf-8" />
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <header className="border-b">
          <div className="container mx-auto flex items-center justify-between px-4 py-4">
            <Link href={`/${locale}`}>
              <h1 className="font-bold text-xl">{dictionary.site.header}</h1>
            </Link>
            <LanguageSwitcher />
          </div>
        </header>
        <main>{children}</main>
        <footer className="mt-12 border-t">
          <div className="container mx-auto px-4 py-6 text-center text-muted-foreground text-sm">
            {dictionary.site.footer.replace(
              '{year}',
              new Date().getFullYear().toString()
            )}
          </div>
        </footer>
      </body>
    </html>
  );
}

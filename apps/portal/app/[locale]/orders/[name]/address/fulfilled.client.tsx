'use client';

import { env } from '@/env';
import type { GetOrdersQuery } from '@/types/admin.generated';
import { Button } from '@repo/design-system/components/ui/button';
import {
  <PERSON>,
  CardContent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import type { Dictionary } from '@repo/internationalization';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function Fullfilled({
  order,
  dictionary,
  statusMessage,
}: {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  statusMessage: string;
}) {
  const router = useRouter();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="font-bold text-3xl uppercase tracking-tight">
          {env.NEXT_PUBLIC_SITE_TITLE ?? dictionary.site.title}
        </h1>
      </div>
      <div className="mx-auto max-w-md">
        <Card>
          <CardHeader>
            <CardTitle>{dictionary.return.change_address}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-6 text-center">
              <AlertCircle className="mx-auto mb-2 h-8 w-8 text-destructive" />
              <h3 className="mb-2 font-medium text-destructive text-xl">
                {dictionary.address?.change_not_allowed ||
                  'Address Change Not Allowed'}
              </h3>
              <p className="mb-4 text-muted-foreground">
                {statusMessage ||
                  dictionary.address?.not_allowed_message ||
                  'Address change is not allowed at this time.'}
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={() => {
                router.push(
                  `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
                    order.email || ''
                  )}`
                );
              }}
              className="w-full"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Services
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

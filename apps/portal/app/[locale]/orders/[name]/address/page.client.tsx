'use client';

import type { GetOrderBasicInfoQuery } from '@/types/admin.generated';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@repo/design-system/components/ui/alert-dialog';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@repo/design-system/components/ui/command';
import {
  Controller,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@repo/design-system/components/ui/popover';
import {
  ScrollArea,
  ScrollBar,
} from '@repo/design-system/components/ui/scroll-area';
import countries from '@repo/design-system/data/countries.json';
import states from '@repo/design-system/data/states.json';
import { cn } from '@repo/design-system/lib/utils';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import { AlertCircle, ArrowLeft, Check, ChevronsUpDown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface AddressPageClientProps {
  order: NonNullable<GetOrderBasicInfoQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
  locale: string;
}

// Import country and state types from location-input
interface Timezone {
  zoneName: string;
  gmtOffset: number;
  gmtOffsetName: string;
  abbreviation: string;
  tzName: string;
}

interface CountryProps {
  id: number;
  name: string;
  iso3: string;
  iso2: string;
  numeric_code: string;
  phone_code: string;
  capital: string;
  currency: string;
  currency_name: string;
  currency_symbol: string;
  tld: string;
  native: string;
  region: string;
  region_id: string;
  subregion: string;
  subregion_id: string;
  nationality: string;
  timezones: Timezone[];
  translations: Record<string, string>;
  latitude: string;
  longitude: string;
  emoji: string;
  emojiU: string;
}

interface StateProps {
  id: number;
  name: string;
  country_id: number;
  country_code: string;
  country_name: string;
  state_code: string;
  type: string | null;
  latitude: string;
  longitude: string;
}

interface AddressFormValues {
  firstName: string;
  lastName: string;
  company?: string;
  phone?: string;
  address1: string;
  address2?: string;
  city: string;
  postalCode: string;
  country: CountryProps | null;
  state: StateProps | null;
}

export default function AddressPageClient({
  order,
  dictionary,
  locale,
}: AddressPageClientProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [formValues, setFormValues] = useState<AddressFormValues | null>(null);
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);
  const [canChangeAddress, setCanChangeAddress] = useState(true);
  const [statusMessage, setStatusMessage] = useState<string | null>(null);

  // Check if address change is allowed
  useEffect(() => {
    const checkAddressChangeStatus = async () => {
      setIsCheckingStatus(true);

      try {
        const response = await fetch('/api/check-address-change', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderName: order.name.replace('#', ''),
            email: order.email,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to check address change status');
        }

        const data = (await response.json()) as {
          success: boolean;
          canChangeAddress: boolean;
          hasTrackingNumber: boolean;
          isFulfilled: boolean;
          isJapanEnv: boolean;
          customerInfo: {
            firstName: string;
            lastName: string;
            phone: string;
            country: string;
          };
          message?: string;
        };

        if (data.success) {
          setCanChangeAddress(data.canChangeAddress);

          // Set status message based on the response
          if (!data.canChangeAddress) {
            if (data.isJapanEnv && data.hasTrackingNumber) {
              setStatusMessage(
                dictionary.address?.tracking_number_message ||
                  'Address change is not allowed because the order has a tracking number.'
              );
            } else if (data.isFulfilled) {
              setStatusMessage(
                dictionary.address?.fulfilled_message ||
                  'Address change is not allowed because the order has been fulfilled.'
              );
            } else {
              setStatusMessage(
                dictionary.address?.not_allowed_generic ||
                  'Address change is not allowed at this time.'
              );
            }
          }
        }
      } catch (error) {
        log.error('Error checking address change status:', { error });
        setStatusMessage(
          dictionary.address?.error_checking ||
            'An error occurred while checking if address change is allowed'
        );
        setCanChangeAddress(false);
      } finally {
        setIsCheckingStatus(false);
      }
    };

    checkAddressChangeStatus();
  }, [order, dictionary.address]);

  // Note: In a production app, you would need to update the GraphQL query in src/lib/shopify.ts
  // to include the firstName, lastName, company, and phone fields in the shippingAddress
  const form = useForm<AddressFormValues>({
    defaultValues: {
      firstName: order.shippingAddress?.firstName || '',
      lastName: order.shippingAddress?.lastName || '',
      company: order.shippingAddress?.company || '',
      phone: order.shippingAddress?.phone || '',
      address1: order.shippingAddress?.address1 || '',
      address2: order.shippingAddress?.address2 || '',
      city: order.shippingAddress?.city || '',
      postalCode: order.shippingAddress?.zip || '',
      country: order.shippingAddress?.country
        ? (countries as CountryProps[]).find(
            (c) =>
              c.name?.toLowerCase() ===
              order.shippingAddress?.country?.toLowerCase()
          )
        : null,
      state: order.shippingAddress?.province
        ? (states as StateProps[]).find(
            (s) =>
              s.name?.toLowerCase() ===
              order.shippingAddress?.province?.toLowerCase()
          )
        : null,
    },
  });

  const handleBack = () => {
    router.push(
      `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
        order.email || ''
      )}`
    );
  };

  // Function to handle form validation and show confirmation dialog
  const onSubmit = (values: AddressFormValues) => {
    // Country is locked and can't be changed, so we don't need to validate it
    // We just need to make sure it exists in the form values
    if (!values.country) {
      // This shouldn't happen since we're not allowing country changes
      // But we'll keep it as a fallback
      const countryMatch = order.shippingAddress?.country
        ? (countries as CountryProps[]).find(
            (c) => c.name === order.shippingAddress?.country
          )
        : null;

      if (countryMatch) {
        values.country = countryMatch;
      }
    }

    // For countries with states, require state selection
    const availableStates = values.country?.id
      ? (states as StateProps[]).filter(
          (state) => state.country_id === values.country?.id
        )
      : [];

    if (availableStates.length > 0 && !values.state) {
      form.setError('state', {
        message:
          dictionary.address?.state_required || 'State/Prefecture is required',
      });
      return;
    }

    // Form is valid, show confirmation dialog
    log.info('Form validated with values:', values);
    setFormValues(values);
    setShowConfirmDialog(true);
  };

  // Function to handle the actual form submission after confirmation
  const handleConfirmedSubmit = async () => {
    if (!formValues) {
      return;
    }

    setIsSubmitting(true);

    // Format the data for submission
    const formattedData = {
      firstName: formValues.firstName,
      lastName: formValues.lastName,
      company: formValues.company || '',
      phone: formValues.phone || '',
      address1: formValues.address1,
      address2: formValues.address2 || '',
      city: formValues.city,
      province: formValues.state?.name || '',
      zip: formValues.postalCode,
      country: formValues.country?.name || '',
    };

    log.info('Formatted data for submission:', formattedData);

    try {
      // Call the API to update the shipping address
      const response = await fetch('/api/update-address', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderName: order.name.replace('#', ''),
          email: order.email,
          address: formattedData,
        }),
      });

      const data = (await response.json()) as {
        success: boolean;
        message: string;
        error?: string;
        details?: unknown;
        order?: {
          id: string;
          shippingAddress?: {
            address1?: string;
            address2?: string;
            city?: string;
            province?: string;
            zip?: string;
            country?: string;
          };
        };
      };

      if (!response.ok) {
        log.error('Error updating address:', data);
        setIsSubmitting(false);
        setShowConfirmDialog(false);
        // Show error message
        return;
      }

      if (!data.success) {
        log.error('Failed to update address:', data);
        setIsSubmitting(false);
        setShowConfirmDialog(false);
        // Show error message
        return;
      }

      log.info('Address updated successfully:', data);

      // Show success message and redirect back
      router.push(
        `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
          order.email || ''
        )}&success=address_updated`
      );
    } catch (error) {
      log.error('Error updating address:', { error });
      setIsSubmitting(false);
      setShowConfirmDialog(false);
      // Show error message
    }
  };

  // Show loading state while checking address change status
  if (isCheckingStatus) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        <p className="font-medium text-lg">
          {dictionary.address?.checking_status ||
            'Checking if address change is allowed...'}
        </p>
      </div>
    );
  }

  // Show error message if address change is not allowed
  if (!canChangeAddress) {
    return (
      <div className="mx-auto max-w-md">
        <Card>
          <CardHeader>
            <CardTitle>{dictionary.return.change_address}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-6 text-center">
              <AlertCircle className="mx-auto mb-2 h-8 w-8 text-destructive" />
              <h3 className="mb-2 font-medium text-destructive text-xl">
                {dictionary.address?.change_not_allowed ||
                  'Address Change Not Allowed'}
              </h3>
              <p className="mb-4 text-muted-foreground">
                {statusMessage ||
                  dictionary.address?.not_allowed_message ||
                  'Address change is not allowed at this time.'}
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleBack} className="w-full">
              <ArrowLeft className="mr-2 h-4 w-4" />{' '}
              {dictionary.address?.back_to_services || 'Back to Services'}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.return.change_address}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
            <div className="rounded-md border bg-muted/50 p-4">
              <h3 className="mb-2 flex items-center font-medium text-sm">
                <span className="mr-2 inline-block h-2 w-2 rounded-full bg-blue-500" />
                {dictionary.address?.original_shipping ||
                  'Original Shipping Address:'}
              </h3>
              <div className="space-y-1 text-sm">
                {(order.shippingAddress?.firstName ||
                  order.shippingAddress?.lastName) && (
                  <p>
                    {order.shippingAddress?.firstName}{' '}
                    {order.shippingAddress?.lastName}
                  </p>
                )}
                {order.shippingAddress?.phone && (
                  <p>📞 {order.shippingAddress?.phone}</p>
                )}
                <p>{order.shippingAddress?.address1}</p>
                {order.shippingAddress?.address2 && (
                  <p>{order.shippingAddress?.address2}</p>
                )}
                <p>
                  {order.shippingAddress?.city},{' '}
                  {order.shippingAddress?.province} {order.shippingAddress?.zip}
                </p>
                <p>{order.shippingAddress?.country}</p>
              </div>
            </div>

            {/* New Shipping Address Preview */}
            <div className="rounded-md border border-green-500 bg-green-50 p-4">
              <h3 className="mb-2 flex items-center font-medium text-sm">
                <span className="mr-2 inline-block h-2 w-2 rounded-full bg-green-500" />
                {dictionary.address?.new_shipping || 'New Shipping Address:'}
              </h3>
              <div className="space-y-1 text-sm">
                {(form.watch('firstName') || form.watch('lastName')) && (
                  <p>
                    {form.watch('firstName')} {form.watch('lastName')}
                  </p>
                )}
                {form.watch('phone') && <p>📞 {form.watch('phone')}</p>}
                <p>
                  {form.watch('address1') ||
                    dictionary.address?.enter_new ||
                    'Enter new address...'}
                </p>
                {form.watch('address2') && <p>{form.watch('address2')}</p>}
                <p>
                  {form.watch('city') ||
                    dictionary.address?.city_placeholder ||
                    'City'}
                  ,
                  {form.watch('state')?.name ||
                    dictionary.address?.state_placeholder ||
                    'State'}{' '}
                  {form.watch('postalCode') ||
                    dictionary.address?.postal_placeholder ||
                    'Postal Code'}
                </p>
                <p>
                  {form.watch('country')?.translations[locale] ??
                    (form.watch('country')?.name ||
                      dictionary.address?.country_placeholder ||
                      'Country')}
                </p>
              </div>
            </div>
          </div>

          <div className="mb-4 border-t pt-4">
            <h3 className="font-medium text-base">
              {dictionary.address?.enter_details ||
                'Enter New Address Details:'}
            </h3>
          </div>

          <Form {...form}>
            <form
              id="address-form"
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-4"
            >
              {/* Contact Information */}
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  rules={{
                    required:
                      dictionary.address?.first_name_required ||
                      'First Name is required',
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {dictionary.address?.first_name || 'First Name'}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="John" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  rules={{
                    required:
                      dictionary.address?.last_name_required ||
                      'Last Name is required',
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {dictionary.address?.last_name || 'Last Name'}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {dictionary.address?.company_optional ||
                          'Company (Optional)'}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Company Name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {dictionary.address?.phone_optional ||
                          'Phone (Optional)'}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="+****************" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Address Fields */}
              <FormField
                control={form.control}
                name="address1"
                rules={{
                  required:
                    dictionary.address?.line1_required ||
                    'Address Line 1 is required',
                }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {dictionary.address?.line1 || 'Address Line 1'}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="123 Main St" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address2"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {dictionary.address?.line2 || 'Address Line 2'}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Apt 4B" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="city"
                  rules={{
                    required:
                      dictionary.address?.city_required || 'City is required',
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {dictionary.address?.city || 'City'}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="New York" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="postalCode"
                  rules={{
                    required:
                      dictionary.address?.postal_required ||
                      'Postal Code is required',
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {dictionary.address?.postal || 'Postal Code'}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="10001" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Location Selector for Country and State */}
              <div className="grid grid-cols-2 gap-4">
                <FormItem>
                  <FormLabel>
                    {dictionary.address?.country || 'Country'}
                  </FormLabel>
                  <Controller
                    control={form.control}
                    name="country"
                    rules={{
                      required:
                        dictionary.address?.country_required ||
                        'Country is required',
                    }}
                    render={({ field }) => (
                      <div className="w-full">
                        {/* Display country as a disabled button to prevent modification */}
                        <Button
                          variant="outline"
                          className="w-full cursor-not-allowed justify-between opacity-70"
                          disabled={true}
                          type="button"
                        >
                          {field.value ? (
                            <div className="flex items-center gap-2">
                              <span>{field.value.emoji}</span>
                              <span>
                                {field.value.translations[locale] ??
                                  field.value.name}
                              </span>
                            </div>
                          ) : (
                            <span>
                              {dictionary.address?.select_country ||
                                'Select Country...'}
                            </span>
                          )}
                        </Button>
                        {/* Add a note explaining why country can't be changed */}
                        <p className="mt-1 text-muted-foreground text-xs">
                          {dictionary.address?.country_locked ||
                            'Country cannot be modified'}
                        </p>
                      </div>
                    )}
                  />
                  {form.formState.errors.country && (
                    <p className="text-destructive text-sm">
                      {form.formState.errors.country.message}
                    </p>
                  )}
                </FormItem>

                <FormItem>
                  <FormLabel>
                    {dictionary.address?.state || 'State/Prefecture'}
                  </FormLabel>
                  <Controller
                    control={form.control}
                    name="state"
                    render={({ field }) => {
                      // Watch for country changes to update state options
                      const selectedCountry = form.watch('country');

                      // Get available states for selected country
                      const availableStates = selectedCountry?.id
                        ? (states as StateProps[]).filter(
                            (state) => state.country_id === selectedCountry.id
                          )
                        : [];

                      // Reset state value if country changes and current state doesn't belong to the country
                      if (
                        field.value &&
                        selectedCountry &&
                        field.value.country_id !== selectedCountry.id
                      ) {
                        // Reset state when country changes
                        setTimeout(() => field.onChange(null), 0);
                      }

                      return (
                        <div className="w-full">
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                // biome-ignore lint/a11y/useSemanticElements: <explanation>
                                role="combobox"
                                disabled={
                                  !selectedCountry ||
                                  availableStates.length === 0
                                }
                                className="w-full justify-between"
                              >
                                {field.value ? (
                                  <span>{field.value.name}</span>
                                ) : (
                                  <span>
                                    {dictionary.address?.select_state ||
                                      'Select State...'}
                                  </span>
                                )}
                                <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[300px] p-0">
                              <Command>
                                <CommandInput
                                  placeholder={
                                    dictionary.address?.search_state ||
                                    'Search state...'
                                  }
                                />
                                <CommandList>
                                  <CommandEmpty>
                                    {dictionary.address?.no_state ||
                                      'No state found.'}
                                  </CommandEmpty>
                                  <CommandGroup>
                                    <ScrollArea className="h-[300px]">
                                      {availableStates.map((state) => (
                                        <CommandItem
                                          key={state.id}
                                          value={state.name}
                                          onSelect={() => {
                                            field.onChange(state);
                                          }}
                                          className="flex items-center justify-between text-sm"
                                        >
                                          <span>{state.name}</span>
                                          <Check
                                            className={cn(
                                              'h-4 w-4',
                                              field.value?.id === state.id
                                                ? 'opacity-100'
                                                : 'opacity-0'
                                            )}
                                          />
                                        </CommandItem>
                                      ))}
                                      <ScrollBar orientation="vertical" />
                                    </ScrollArea>
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </div>
                      );
                    }}
                  />
                  {form.formState.errors.state && (
                    <p className="text-destructive text-sm">
                      {form.formState.errors.state.message}
                    </p>
                  )}
                  <p className="text-muted-foreground text-xs">&nbsp;</p>
                </FormItem>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2 "
          >
            <ArrowLeft className="h-4 w-4" />
            {dictionary.button.back}
          </Button>

          <Button
            form="address-form"
            type="submit"
            disabled={isSubmitting || form.formState.isSubmitting}
            className="cursor-pointer"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {dictionary.message.loading || 'Loading...'}
              </div>
            ) : (
              dictionary.button.submit
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {dictionary.address?.confirm_title || 'Confirm Address Change'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {dictionary.address?.confirm_description ||
                'Are you sure you want to update the shipping address for this order?'}
            </AlertDialogDescription>
          </AlertDialogHeader>

          {formValues && (
            <div className="my-4 grid grid-cols-1 gap-6 md:grid-cols-2">
              <div className="rounded-md border bg-muted/50 p-4">
                <h3 className="mb-2 flex items-center font-medium text-sm">
                  <span className="mr-2 inline-block h-2 w-2 rounded-full bg-blue-500" />
                  {dictionary.address?.original_address || 'Original Address:'}
                </h3>
                <div className="space-y-1 text-sm">
                  {(order.shippingAddress?.firstName ||
                    order.shippingAddress?.lastName) && (
                    <p>
                      {order.shippingAddress?.firstName}{' '}
                      {order.shippingAddress?.lastName}
                    </p>
                  )}
                  {order.shippingAddress?.phone && (
                    <p>📞 {order.shippingAddress?.phone}</p>
                  )}
                  <p>{order.shippingAddress?.address1}</p>
                  {order.shippingAddress?.address2 && (
                    <p>{order.shippingAddress?.address2}</p>
                  )}
                  <p>
                    {order.shippingAddress?.city},{' '}
                    {order.shippingAddress?.province}{' '}
                    {order.shippingAddress?.zip}
                  </p>
                  <p>{order.shippingAddress?.country}</p>
                </div>
              </div>

              <div className="rounded-md border border-green-500 bg-green-50 p-4">
                <h3 className="mb-2 flex items-center font-medium text-sm">
                  <span className="mr-2 inline-block h-2 w-2 rounded-full bg-green-500" />
                  {dictionary.address?.new_address || 'New Address:'}
                </h3>
                <div className="space-y-1 text-sm">
                  {(formValues.firstName || formValues.lastName) && (
                    <p>
                      {formValues.firstName} {formValues.lastName}
                    </p>
                  )}
                  {formValues.company && <p>{formValues.company}</p>}
                  <p>{formValues.address1}</p>
                  {formValues.address2 && <p>{formValues.address2}</p>}
                  <p>
                    {formValues.city}, {formValues.state?.name || ''}{' '}
                    {formValues.postalCode}
                  </p>
                  <p>{formValues.country?.name}</p>
                  {formValues.phone && <p>📞 {formValues.phone}</p>}
                </div>
              </div>
            </div>
          )}

          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowConfirmDialog(false)}>
              {dictionary.address?.cancel || 'Cancel'}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmedSubmit}
              disabled={isSubmitting}
              className={isSubmitting ? 'cursor-not-allowed opacity-50' : ''}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {dictionary.address?.processing || 'Processing...'}
                </div>
              ) : (
                dictionary.address?.confirm_change || 'Confirm Change'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

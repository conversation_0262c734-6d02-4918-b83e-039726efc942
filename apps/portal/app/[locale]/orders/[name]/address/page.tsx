import { env } from '@/env';
import { verifyOrderAccess } from '@/lib/shopify';
import { getDictionary } from '@repo/internationalization';
import { redirect } from 'next/navigation';
import AddressPageClient from './page.client';
// import Fullfilled from "./fulfilled.client"

interface AddressPageProps {
  params: Promise<{
    name: string;
    locale: string;
  }>;
  searchParams: Promise<{
    email?: string;
  }>;
}

export default async function AddressPage({
  params,
  searchParams,
}: AddressPageProps) {
  const { name, locale } = await params;
  const { email } = await searchParams;

  const dictionary = await getDictionary(locale);

  // Verify order access with email if provided
  const order = await verifyOrderAccess(name, email);

  // NOTE: for SSR
  // const order = await getOrderByName(name, email)

  // If order not found or email verification failed
  if (!order) {
    // Redirect to home page with error message, including the locale
    redirect(
      `/${locale}?error=verification_failed&orderName=${encodeURIComponent(name)}`
    );
  }

  // NOTE: for SSR
  // if (order.fulfillments.length > 0) {
  //   const isJapanEnv = process.env.NEXT_PUBLIC_REGION === "JP"
  //   const hasTrackingNumber = true // TODO: fetch casefinite order status

  //   const statusMessage =
  //     isJapanEnv && hasTrackingNumber
  //       ? "Address change is not allowed because the order has a tracking number."
  //       : "Address change is not allowed because the order has been fulfilled."

  //   return <Fullfilled order={order} dictionary={dictionary} statusMessage={statusMessage} />
  // }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="font-bold text-3xl uppercase tracking-tight">
          {env.NEXT_PUBLIC_SITE_TITLE ?? dictionary.site.title}
        </h1>
      </div>
      <AddressPageClient
        order={order}
        dictionary={dictionary}
        locale={locale}
      />
    </div>
  );
}

'use client';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import type { Dictionary } from '@repo/internationalization';
import * as z from 'zod';

// Define the form schema
const formSchema = z.object({
  recipientName: z.string().min(1, 'Recipient name is required'),
  companyName: z.string().optional(),
});

export type ReceiptFormValues = z.infer<typeof formSchema>;

interface ReceiptFormProps {
  defaultValues: {
    recipientName: string;
    companyName?: string;
  };
  onSubmit: (values: ReceiptFormValues) => void;
  dictionary?: Dictionary | null;
  disabled?: boolean;
}

export default function ReceiptForm({
  defaultValues,
  onSubmit,
  dictionary,
  disabled = false,
}: ReceiptFormProps) {
  // Initialize the form
  const form = useForm<ReceiptFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  // Handle form submission
  function handleSubmit(values: ReceiptFormValues) {
    onSubmit(values);
  }

  // Default text if dictionary is not loaded yet
  const t = dictionary?.receipt?.form || {
    recipient_name: 'Recipient Name',
    recipient_placeholder: 'Enter recipient name',
    recipient_description: 'The name that will appear on the receipt',
    company_name: 'Company Name (Optional)',
    company_placeholder: 'Enter company name',
    company_description: 'Company name to include on the receipt',
    generate: 'Generate Receipt',
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="recipientName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t.recipient_name}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t.recipient_placeholder}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>{t.recipient_description}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="companyName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t.company_name}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t.company_placeholder}
                  disabled={disabled}
                  {...field}
                />
              </FormControl>
              <FormDescription>{t.company_description}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end">
          <Button type="submit" className="cursor-pointer" disabled={disabled}>
            {t.generate}
          </Button>
        </div>
      </form>
    </Form>
  );
}

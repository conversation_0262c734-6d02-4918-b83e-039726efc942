'use client';

import type { GetOrdersQuery } from '@/types/admin.generated';
import type { Dictionary } from '@repo/internationalization';
import React from 'react';

// Settings - keeping for reference but not using directly
// const SETTINGS = {
//   date_format: "%Y年%m月%d日 %H:%M",
//   show_cart_attributes: true,
// }

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');

  return `${year}年${month}月${day}日 ${hours}:${minutes}`;
}

// Helper function to format money
function formatMoney(amount: string | number): string {
  const numAmount =
    typeof amount === 'string' ? Number.parseFloat(amount) : amount;
  return new Intl.NumberFormat('ja-JP', {
    style: 'currency',
    currency: 'JPY',
    minimumFractionDigits: 0,
  }).format(numAmount);
}

// Helper function to get current date in Japanese format
function getCurrentDate(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();

  return `${year}年${month}月${day}日`;
}

interface ReceiptProps {
  orderName: string;
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  customRecipientName?: string;
  customCompanyName?: string;
  dictionary?: Dictionary | null;
}

export default function Receipt({
  order,
  customRecipientName,
  customCompanyName,
  dictionary,
}: ReceiptProps) {
  // Default labels if dictionary is not loaded yet
  const t = dictionary?.receipt?.pdf || {
    store_name: 'ケースフィニット株式会社',
    store_zip: '105-0011',
    store_address: '東京都港区芝公園1-8-20',
    store_address2: '',
    store_phone: '',
    merchant_id: 'T7013301046118',
    invoice: '領　収　書',
    already_printed_out: '( 再　発　行 )',
    order_number: '注文番号',
    processed_at: '注文日時',
    shipping_address: '送付先情報',
    customer_information: '注文者情報',
    billing_address: '注文日時',
    phone: 'TEL',
    payment_method: 'お支払い方法',
    shipping_method: '発送方法',
    products: '商品',
    price_single: '単価',
    quantity: '個数',
    price_total: '合計',
    total_discounts: '割引',
    subtotal_price: '小計',
    shipping_price: '送料',
    total_price: 'お支払い金額',
    total_refunds: '返金額合計',
    recipient_suffix: '様',
    tax_included: '(税込)',
    for_products: '但し お品代として',
    receipt_confirmation: '上記、正に領収致しました。',
    reduced_tax_note: '※ 軽減税率対象品目です',
    tax: '消費税',
    breakdown: '内訳',
    tax_rate_subject: '{rate}%対象',
    consumption_tax: '消費税({rate}%)',
    special_notes: '特記事項',
    credit_card_note_1:
      '※ クレジットカード決済の場合には、金銭の受領事実がありませんので、表題が「領収書」',
    credit_card_note_2:
      '　となっていても、印紙税法基本通達第17号の1文書には該当しません。',
    credit_card_note_3: '※ 5万円を超えていても収入印紙は貼付されません。',
  };
  // Extract line items from the GraphQL response
  const lineItems = order.lineItems?.edges?.map((edge) => edge.node) || [];

  // Extract addresses
  const shippingAddress = order.shippingAddress || {};
  const billingAddress = order.billingAddress || {};

  return (
    <div className="relative mx-auto box-border w-[210mm] bg-white p-[15mm] font-sans">
      {/* Header - Title centered */}
      <div className="mb-[5mm] flex w-full flex-row">
        <div className="my-auto flex-[2] text-center text-[28px]">
          {t.invoice}
        </div>
      </div>

      {/* Date - Right aligned */}
      <div className="mb-[5mm] flex-1 text-right">{getCurrentDate()}</div>

      {/* Customer and Store Information */}
      <table className="my-[5mm] mb-[5mm] w-full border-collapse">
        <tbody>
          <tr>
            <td className="w-1/2 text-center text-base leading-6">
              {customRecipientName ||
                `${billingAddress.lastName} ${billingAddress.firstName}`}{' '}
              {t.recipient_suffix}
              <br />
              {customCompanyName ? (
                <>
                  {customCompanyName}
                  <br />
                </>
              ) : (
                shippingAddress.company && (
                  <>
                    {shippingAddress.company}
                    <br />
                  </>
                )
              )}
              {formatMoney(order.totalPriceSet?.shopMoney?.amount || 0)}
              {t.tax_included}
              <br />
              {t.for_products}
              <br />
              {t.receipt_confirmation}
            </td>
            <td className="w-1/2 pl-[12mm] text-left">
              {t.store_name}
              <br />〒{t.store_zip}
              <br />
              {t.store_address}
              <br />
              {t.store_address2 && (
                <>
                  {t.store_address2}
                  <br />
                </>
              )}
              {t.store_phone && (
                <>
                  {t.store_phone}
                  <br />
                </>
              )}
              <br />
              {t.merchant_id && <>登録番号：{t.merchant_id}</>}
            </td>
          </tr>
        </tbody>
      </table>

      <hr className="my-[2mm] border-0 border-gray-200 border-t border-solid" />

      {/* Order Information */}
      <div className="text-sm">
        {t.order_number}： {order.name}
        <br />
        {t.processed_at}： {formatDate(order.createdAt)}
      </div>

      <hr className="my-[2mm] border-0 border-gray-200 border-t border-solid" />

      {/* Product Table */}
      <table className="w-full border-collapse">
        <thead>
          <tr>
            <th className="w-[60%] font-normal" style={{ textAlign: 'left' }}>
              {t.products}
            </th>
            <th className="w-[15%] font-normal" style={{ textAlign: 'right' }}>
              {t.price_single}
            </th>
            <th className="w-[10%] font-normal" style={{ textAlign: 'right' }}>
              {t.quantity}
            </th>
            <th className="w-[15%] font-normal" style={{ textAlign: 'right' }}>
              {t.price_total}
            </th>
          </tr>
        </thead>
        <tbody>
          {lineItems.map((item) => (
            <tr key={item.id}>
              <td style={{ textAlign: 'left' }}>
                {item.title}
                {item.variantTitle && (
                  <span className="block text-gray-500 text-xs">
                    {item.variantTitle}
                  </span>
                )}
              </td>
              <td style={{ textAlign: 'right' }}>
                {formatMoney(item.originalUnitPriceSet?.shopMoney?.amount || 0)}
              </td>
              <td style={{ textAlign: 'right' }}>{item.quantity}</td>
              <td style={{ textAlign: 'right' }}>
                {formatMoney(
                  Number.parseFloat(
                    item.originalUnitPriceSet?.shopMoney?.amount || '0'
                  ) * item.quantity
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <hr className="my-[2mm] border-0 border-gray-200 border-t border-solid" />

      {/* Totals Section */}
      <div className="w-full">
        <table className="w-full">
          <tbody>
            <tr>
              <td className="w-1/2 text-left align-top">
                ※ 軽減税率対象品目です
              </td>
              <td className="w-1/2">
                <table className="w-full border-collapse">
                  <tbody>
                    <tr>
                      <td className="pr-[5mm]" style={{ textAlign: 'right' }}>
                        {t.shipping_price}
                      </td>
                      <td style={{ textAlign: 'right' }}>
                        {formatMoney(
                          order.totalShippingPriceSet?.shopMoney?.amount || 0
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td className="pr-[5mm]" style={{ textAlign: 'right' }}>
                        {t.subtotal_price}
                      </td>
                      <td style={{ textAlign: 'right' }}>
                        {formatMoney(
                          order.subtotalPriceSet?.shopMoney?.amount || 0
                        )}
                      </td>
                    </tr>
                    <tr>
                      <td className="pr-[5mm]" style={{ textAlign: 'right' }}>
                        消費税
                      </td>
                      <td style={{ textAlign: 'right' }}>
                        {formatMoney(order.totalTaxSet?.shopMoney?.amount || 0)}
                      </td>
                    </tr>
                    <tr>
                      <td className="pr-[5mm]" style={{ textAlign: 'right' }}>
                        {t.total_price}
                      </td>
                      <td style={{ textAlign: 'right' }}>
                        {formatMoney(
                          order.totalPriceSet?.shopMoney?.amount || 0
                        )}
                      </td>
                    </tr>

                    <tr>
                      <td
                        className="pt-[5mm] pr-[5mm]"
                        style={{ textAlign: 'right' }}
                      >
                        内訳
                      </td>
                      <td />
                    </tr>

                    {order.taxLines?.map((taxLine) => {
                      if (!taxLine.rate) {
                        return null;
                      }

                      const taxRate = taxLine.rate;
                      const taxRatePercent = (taxRate * 100).toFixed(0);

                      // Calculate total for items with this tax rate
                      const taxableAmount =
                        order.lineItems?.edges
                          ?.filter((edge) => {
                            // Find line items with this tax rate
                            const itemTaxLines = edge.node.taxLines || [];
                            return itemTaxLines.some(
                              (tl) => tl.rate === taxRate
                            );
                          })
                          .reduce((sum: number, edge) => {
                            return (
                              sum +
                              Number.parseFloat(
                                edge.node.originalUnitPriceSet?.shopMoney
                                  ?.amount || '0'
                              ) *
                                edge.node.quantity
                            );
                          }, 0) || 0;

                      return (
                        <React.Fragment key={`tax-rate-${taxRate}`}>
                          <tr>
                            <td
                              className="pr-[5mm]"
                              style={{ textAlign: 'right' }}
                            >
                              {taxRatePercent}%対象
                            </td>
                            <td style={{ textAlign: 'right' }}>
                              {formatMoney(taxableAmount)}
                            </td>
                          </tr>
                          <tr>
                            <td
                              className="pr-[5mm]"
                              style={{ textAlign: 'right' }}
                            >
                              消費税({taxRatePercent}%)
                            </td>
                            <td style={{ textAlign: 'right' }}>
                              {formatMoney(
                                taxLine.priceSet?.shopMoney?.amount || 0
                              )}
                            </td>
                          </tr>
                        </React.Fragment>
                      );
                    })}
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <hr className="my-[5mm] border-0 border-gray-200 border-t border-solid" />

      {/* Address Information */}
      <div className="my-[5mm] flex w-full text-sm">
        {shippingAddress && (
          <div className="pr-[5mm] align-top">
            <p>{t.shipping_address}</p>

            <p>
              {shippingAddress.company && (
                <>
                  {shippingAddress.company}
                  <br />
                </>
              )}
              {shippingAddress.lastName} {shippingAddress.firstName}{' '}
              {t.recipient_suffix}
              <br />〒{shippingAddress.zip}
              <br />
              {shippingAddress.province && (
                <>
                  {shippingAddress.province}
                  {shippingAddress.city}
                  {shippingAddress.address1}
                  <br />
                </>
              )}
              {!shippingAddress.province && (
                <>
                  {shippingAddress.address1 && (
                    <>
                      {shippingAddress.address1}
                      <br />
                    </>
                  )}
                  {shippingAddress.address2 && (
                    <>
                      {shippingAddress.address2}
                      <br />
                    </>
                  )}
                  {shippingAddress.city && (
                    <>
                      {shippingAddress.city}
                      <br />
                    </>
                  )}
                </>
              )}
              {shippingAddress.phone && (
                <>
                  {t.phone} {shippingAddress.phone}
                </>
              )}
            </p>
          </div>
        )}

        {billingAddress && (
          <div className="pr-[5mm] align-top">
            <p>{t.customer_information}</p>

            <p>
              {billingAddress.company && (
                <>
                  {billingAddress.company}
                  <br />
                </>
              )}
              {billingAddress.lastName} {billingAddress.firstName}{' '}
              {t.recipient_suffix}
              <br />〒{billingAddress.zip}
              <br />
              {billingAddress.province && (
                <>
                  {billingAddress.province}
                  {billingAddress.city}
                  {billingAddress.address1}
                  <br />
                </>
              )}
              {!billingAddress.province && (
                <>
                  {billingAddress.address1 && (
                    <>
                      {billingAddress.address1}
                      <br />
                    </>
                  )}
                  {billingAddress.address2 && (
                    <>
                      {billingAddress.address2}
                      <br />
                    </>
                  )}
                  {billingAddress.city && (
                    <>
                      {billingAddress.city}
                      <br />
                    </>
                  )}
                </>
              )}
              {billingAddress.phone && (
                <>
                  {t.phone} {billingAddress.phone}
                </>
              )}
            </p>
          </div>
        )}

        {order.shippingLine && (
          <>
            <p>{t.shipping_method}</p>
            <p>{order.shippingLine.title}</p>
          </>
        )}
      </div>

      {/* Footer Notes */}
      <div className="mt-[5mm] ml-[5mm] w-full text-left text-xs leading-[18px]">
        {t.special_notes}
        <br />
        {t.credit_card_note_1}
        <br />
        {t.credit_card_note_2}
        <br />
        {t.credit_card_note_3}
      </div>
    </div>
  );
}

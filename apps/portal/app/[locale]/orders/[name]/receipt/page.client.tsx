'use client';

import ReceiptDialog from '@/app/[locale]/orders/[name]/receipt/_components/receipt-dialog';
import type { GetOrdersQuery } from '@/types/admin.generated';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import type { Dictionary } from '@repo/internationalization';
import { ArrowLeft, Printer } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ReceiptPageClientProps {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}

export default function ReceiptPageClient({
  order,
  dictionary,
}: ReceiptPageClientProps) {
  const router = useRouter();

  const handleBack = () => {
    router.push(
      `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
        order.email || ''
      )}`
    );
  };

  return (
    <div className="mx-auto max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>
            {dictionary.return.download_receipt || 'Download Receipt'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="rounded-lg bg-muted p-6 text-center">
            <p className="text-lg">
              {dictionary.return.download_receipt_description ||
                'Download a receipt for your order.'}
            </p>
          </div>

          <div className="mt-8 flex justify-between text-center">
            <Button
              variant="outline"
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {dictionary.button.back}
            </Button>
            <ReceiptDialog
              orderName={order.name}
              order={order}
              trigger={
                <Button className="flex items-center gap-2">
                  <Printer className="h-4 w-4" />
                  {dictionary.receipt.form.generate || 'Generate Receipt'}
                </Button>
              }
              dictionary={dictionary}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { verifyOrderAccess } from '@/lib/shopify';
import { redirect } from 'next/navigation';

// Define the props type for the page component
type OrderPageProps = {
  params: Promise<{
    locale: string;
    name: string;
  }>;
  searchParams: Promise<{
    email?: string;
  }>;
};

export default async function OrderPage({
  params,
  searchParams,
}: OrderPageProps) {
  const { name, locale } = await params;
  const { email } = await searchParams;

  // Verify order access with email if provided
  const order = await verifyOrderAccess(name, email);

  // If order not found or email verification failed
  if (!order) {
    // Redirect to home page with error message, including the locale
    redirect(
      `/${locale}?error=verification_failed&orderName=${encodeURIComponent(name)}`
    );
  }

  // Redirect to service selection page
  redirect(
    `/${locale}/orders/${name}/services?email=${encodeURIComponent(email || '')}`
  );
}

import type { GetOrdersQuery } from '@/types/admin.generated';
import { log } from '@repo/observability/log';
import type { FlowState, ReturnReason } from './types';

// Calculate the return period based on order data
export function calculateReturnPeriod(
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>,
  updateFlowState: (updates: Partial<FlowState>) => void
) {
  // Get the current date
  const currentDate = new Date();

  // Determine the reference date for return period calculation
  // First, try to use the most recent fulfillment's deliveredAt date
  let referenceDate: Date | null = null;
  let referenceType = 'fulfillment';

  // Check if the order has fulfillments
  if (order.fulfillments && order.fulfillments.length > 0) {
    // Sort fulfillments by date (most recent first)
    const sortedFulfillments = [...order.fulfillments].sort((a, b) => {
      // Use deliveredAt if available, otherwise use updatedAt or createdAt
      const dateA = a.deliveredAt || a.updatedAt || a.createdAt;
      const dateB = b.deliveredAt || b.updatedAt || b.createdAt;

      if (!dateA) {
        return 1;
      }
      if (!dateB) {
        return -1;
      }

      return new Date(dateB).getTime() - new Date(dateA).getTime();
    });

    // Get the most recent fulfillment
    const latestFulfillment = sortedFulfillments[0];

    // Use the delivered date if available, otherwise use updated or created date
    const fulfillmentDate =
      latestFulfillment.deliveredAt ||
      latestFulfillment.updatedAt ||
      latestFulfillment.createdAt;

    if (fulfillmentDate) {
      referenceDate = new Date(fulfillmentDate);
    }
  }

  // If no fulfillment date is available, fall back to the order's processedAt date
  if (!referenceDate && order.processedAt) {
    referenceDate = new Date(order.processedAt);
    referenceType = 'processed';
  }

  // If still no reference date, use the order's createdAt date as a last resort
  if (!referenceDate && order.createdAt) {
    referenceDate = new Date(order.createdAt);
    referenceType = 'created';
  }

  // If we have a reference date, calculate the difference in days
  if (referenceDate) {
    const diffTime = currentDate.getTime() - referenceDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    log.info(
      `Return period calculation: ${diffDays} days since order was ${referenceType}`
    );

    // Update the flow state with the order date and return period flags
    updateFlowState({
      orderDate: referenceDate,
      isWithinReturnPeriod: diffDays <= 10,
      isSpecialApproval: diffDays > 10 && diffDays <= 30,
      isExpired: diffDays > 30,
      hasDeliveryDate: referenceType === 'fulfillment',
    });
  } else {
    log.error('No reference date found for return period calculation');
  }
}

// Calculate refund fee based on reason
export function calculateRefundFee(
  reason: ReturnReason,
  totalAmount: number
): number {
  let refundFee = 0;
  if (reason === 'wrong_size_model') {
    // Calculate 10% of total amount as refund fee
    refundFee = totalAmount * 0.1;
  }
  return refundFee;
}

// Check if the current step is valid
export function isStepValid(
  flowState: FlowState,
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges']
): boolean {
  // If the order is expired, don't allow proceeding
  if (flowState.isExpired) {
    return false;
  }

  // If all selected items have been processed, don't allow proceeding
  if (
    flowState.lineItemsProcessed &&
    selectedItems.every((item) =>
      flowState.lineItemsProcessed?.includes(item.node.id)
    )
  ) {
    return false;
  }

  switch (flowState.step) {
    case 1: // Reason selection
      // Allow proceeding with any reason, including ineligible ones
      return !!flowState.returnReason;

    case 2: // Accessories check
      return flowState.hasAccessories === true;

    case 3: {
      // Return/Exchange details
      if (
        flowState.returnReason === 'defect' ||
        flowState.returnReason === 'wrong_product'
      ) {
        // For defect or wrong_product, require exchange type selection first
        if (!flowState.exchangeType) {
          return false;
        }

        // If it's a defect, we need additional details
        if (flowState.returnReason === 'defect') {
          // For all defect types, require photos
          return (
            (!!flowState.defectType &&
              !!flowState.defectDetails &&
              (!!flowState.defectPhotoUrl ||
                (flowState.defectPhotos &&
                  flowState.defectPhotos.length > 0))) ??
            false
          );
        }
      }
      return true;
    }

    case 4:
      // How to exchange & how to return guide
      // Require return label option selection
      return !!flowState.returnLabelOption;

    case 5: // Review
      // Require both terms and policy checkboxes to be checked
      return (
        flowState.termsChecked === true && flowState.policyChecked === true
      );

    default:
      return false;
  }
}

// Check if return is not allowed (expired or all items processed)
export function isReturnNotAllowed(
  flowState: FlowState,
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges']
): boolean {
  return (
    (flowState.isExpired ||
      (flowState.lineItemsProcessed &&
        selectedItems.every((item) =>
          flowState.lineItemsProcessed?.includes(item.node.id)
        ))) ??
    false
  );
}

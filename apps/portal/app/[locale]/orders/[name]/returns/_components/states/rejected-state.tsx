'use client';

import type { GetOrdersQuery } from '@/types/admin.generated';
import type { Dictionary } from '@repo/internationalization';
import { AlertCircle } from 'lucide-react';
import SelectedItemsDisplay from '../selected-items-display';
import type { ReturnReason } from '../types';

interface RejectedStateProps {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  dictionary: Dictionary;
  returnReason?: ReturnReason;
}

export default function RejectedState({
  selectedItems,
  dictionary,
  returnReason,
}: RejectedStateProps) {
  return (
    <div className="space-y-6">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-6 text-center">
        <AlertCircle className="mx-auto mb-2 h-8 w-8 text-destructive" />
        <h3 className="mb-2 font-medium text-destructive text-xl">
          {dictionary.return.request_rejected || 'Return Request Rejected'}
        </h3>
        <p className="mb-4 text-muted-foreground">
          {dictionary.return.rejection_message ||
            "We're sorry, but we cannot accept your return request for the following reason:"}
        </p>
        <p className="font-medium">
          {returnReason && dictionary.return.reason[returnReason]}
        </p>
      </div>

      <div className="rounded-lg bg-muted p-4">
        <h4 className="mb-2 font-medium">{"What's Next?"}</h4>
        <p className="text-sm">
          {
            'If you have any questions or believe this is an error, please contact our customer support team for assistance.'
          }
        </p>
      </div>
    </div>
  );
}

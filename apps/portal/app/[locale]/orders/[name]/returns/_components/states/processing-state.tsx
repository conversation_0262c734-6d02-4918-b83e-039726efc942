'use client';

import type { Dictionary } from '@repo/internationalization';

interface ProcessingStateProps {
  dictionary: Dictionary;
}

export default function ProcessingState({ dictionary }: ProcessingStateProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="mb-4 h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent" />
      <p className="font-medium text-lg">
        {dictionary.return.checking_request ||
          'We are checking your request...'}
      </p>
      <p className="mt-2 text-muted-foreground text-sm">
        {dictionary.return.please_wait || 'Please wait a moment...'}
      </p>
    </div>
  );
}

'use client';

import { Card, CardContent } from '@repo/design-system/components/ui/card';
import { Checkbox } from '@repo/design-system/components/ui/checkbox';
import { Label } from '@repo/design-system/components/ui/label';
import { formatCurrency } from '@repo/design-system/lib/format';
import { useState } from 'react';
import SelectedItemsDisplay from '../selected-items-display';
import type { StepProps } from '../types';

export default function Step5Review({
  flowState,
  selectedItems,
  dictionary,
  totalAmount,
  updateFlowState,
}: StepProps) {
  const [termsChecked, setTermsChecked] = useState(
    flowState.termsChecked || false
  );
  const [policyChecked, setPolicyChecked] = useState(
    flowState.policyChecked || false
  );

  // Handle checkbox changes directly
  const handleTermsChange = (checked: boolean) => {
    setTermsChecked(checked);
    updateFlowState({
      termsChecked: checked,
    });
  };

  const handlePolicyChange = (checked: boolean) => {
    setPolicyChecked(checked);
    updateFlowState({
      policyChecked: checked,
    });
  };

  return (
    <div className="space-y-4">
      <SelectedItemsDisplay
        selectedItems={selectedItems}
        dictionary={dictionary}
      />

      <h3 className="font-medium text-lg">
        {flowState.exchangeType === 'exchange'
          ? 'Review Your Exchange'
          : 'Review Your Return'}
      </h3>

      <Card className="overflow-hidden">
        <CardContent className="pt-4">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">
                {dictionary.return.type || 'Type:'}
              </span>
              <span className="font-medium">
                {flowState.exchangeType === 'exchange'
                  ? dictionary.return.exchange || 'Exchange'
                  : dictionary.return.return || 'Return'}
              </span>
            </div>

            <div className="flex justify-between">
              <span className="text-muted-foreground">
                {dictionary.return.reason_label || 'Reason:'}
              </span>
              <span className="font-medium">
                {flowState.returnReason &&
                  dictionary.return.reason[flowState.returnReason]}
              </span>
            </div>

            {/* Only show return label option for returns, not exchanges */}
            {(!flowState.exchangeType ||
              flowState.exchangeType === 'return') && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">{'Return Label:'}</span>
                <span className="font-medium">
                  {flowState.returnLabelOption === 'print'
                    ? 'Print yourself'
                    : 'Request return envelope'}
                </span>
              </div>
            )}

            <div className="flex justify-between">
              <span className="text-muted-foreground">
                {dictionary.return.total_amount || 'Total Amount:'}
              </span>
              <span className="font-medium">
                {formatCurrency(
                  totalAmount.toString(),
                  selectedItems[0].node.originalUnitPriceSet.shopMoney
                    .currencyCode === 'JPY'
                    ? 'ja-JP'
                    : 'en-US',
                  selectedItems[0].node.originalUnitPriceSet.shopMoney
                    .currencyCode
                )}
              </span>
            </div>

            {/* Only show refund fee for returns, not exchanges */}
            {(!flowState.exchangeType || flowState.exchangeType === 'return') &&
            flowState.refundFee &&
            flowState.refundFee > 0 ? (
              <>
                <div className="mt-2 border-t pt-2" />
                <div className="flex justify-between text-amber-700">
                  <span className="font-medium">{'Refund Fee:'}</span>
                  <span className="font-medium">
                    {formatCurrency(
                      flowState.refundFee.toString(),
                      selectedItems[0].node.originalUnitPriceSet.shopMoney
                        .currencyCode === 'JPY'
                        ? 'ja-JP'
                        : 'en-US',
                      selectedItems[0].node.originalUnitPriceSet.shopMoney
                        .currencyCode
                    )}
                  </span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>{'Final Refund Amount:'}</span>
                  <span>
                    {formatCurrency(
                      (totalAmount - flowState.refundFee).toString(),
                      selectedItems[0].node.originalUnitPriceSet.shopMoney
                        .currencyCode === 'JPY'
                        ? 'ja-JP'
                        : 'en-US',
                      selectedItems[0].node.originalUnitPriceSet.shopMoney
                        .currencyCode
                    )}
                  </span>
                </div>
              </>
            ) : (
              <></>
            )}

            {/* Show special approval message if applicable */}
            {flowState.isSpecialApproval && (
              <div className="mt-2 border-t pt-2 text-amber-700">
                <p className="text-sm">
                  <span className="font-medium">Special Approval:</span> This
                  return/exchange is being processed as an exception since it's
                  more than 10 days after the order date.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <div className="space-y-2">
        <div className="flex items-start space-x-2">
          <Checkbox
            id="terms"
            checked={termsChecked}
            onCheckedChange={(checked) => handleTermsChange(checked === true)}
          />
          <Label htmlFor="terms" className="text-sm">
            {dictionary.return.understand_condition ||
              'I understand that I need to return the items in their original condition with all tags and packaging.'}
          </Label>
        </div>

        <div className="flex items-start space-x-2">
          <Checkbox
            id="policy"
            checked={policyChecked}
            onCheckedChange={(checked) => handlePolicyChange(checked === true)}
          />
          <Label htmlFor="policy" className="text-sm">
            {dictionary.return.agree_policy ||
              'I have read and agree to the return policy.'}
          </Label>
        </div>
      </div>
    </div>
  );
}

'use client';

import type { GetOrdersQuery } from '@/types/admin.generated';
import { formatCurrency } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import Image from 'next/image';

interface SelectedItemsDisplayProps {
  selectedItems: NonNullable<
    GetOrdersQuery['orders']['edges'][0]['node']
  >['lineItems']['edges'];
  dictionary: Dictionary;
}

export default function SelectedItemsDisplay({
  selectedItems,
  dictionary,
}: SelectedItemsDisplayProps) {
  return (
    <div className="mb-6 rounded-md border bg-gray-50 p-4">
      <h4 className="mb-3 font-medium text-muted-foreground-700 text-sm">
        {dictionary.return.selected_items || 'Selected Items for Return'}
      </h4>
      <div className="space-y-3">
        {selectedItems.map((item) => (
          <div key={item.node.id} className="flex items-center">
            <div className="mr-3 flex-shrink-0">
              <div className="relative h-12 w-12 overflow-hidden rounded">
                {item.node.image && (
                  <Image
                    src={item.node.image.url}
                    alt={item.node.title}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                )}
              </div>
            </div>
            <div className="flex-grow">
              <p className="text-muted-foreground text-xs">
                {item.node.variantTitle}
              </p>
              <p className="font-medium text-md">{item.node.title}</p>
            </div>
            <div className="flex-shrink-0 text-right">
              <p className="font-medium text-sm">
                {formatCurrency(
                  item.node.originalUnitPriceSet?.shopMoney.amount,
                  item.node.originalUnitPriceSet.shopMoney.currencyCode ===
                    'JPY'
                    ? 'ja-JP'
                    : 'en-US',
                  item.node.originalUnitPriceSet.shopMoney.currencyCode
                )}
              </p>
              <p className="text-muted-foreground text-xs">
                {dictionary.order.quantity}: {item.node.quantity}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

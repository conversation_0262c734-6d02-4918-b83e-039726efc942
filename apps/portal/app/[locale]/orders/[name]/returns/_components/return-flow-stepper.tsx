'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { log } from '@repo/observability/log';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  calculateReturnPeriod,
  isReturnNotAllowed,
  isStepValid,
} from './return-flow-utils';
import type { FlowState, ReturnFlowStepperProps } from './types';

import Step1ReasonSelection from './steps/step-1-reason-selection';
import Step2AccessoriesCheck from './steps/step-2-accessories-check';
import Step3ReturnDetails from './steps/step-3-return-details';
import Step4ReturnInstructions from './steps/step-4-return-instructions';
import Step5Review from './steps/step-5-review';

import { getReturnNumber } from '@/lib/utils';
import CompletedState from './states/completed-state';
import ProcessingState from './states/processing-state';
import RejectedState from './states/rejected-state';
import ReturnNotAllowedState from './states/return-not-allowed-state';

export default function ReturnFlowStepper({
  selectedItems,
  order,
  dictionary,
  onComplete,
  onCancel,
}: ReturnFlowStepperProps) {
  // Use a state object to track the entire flow
  const [flowState, setFlowState] = useState<FlowState>({
    step: 1,
    bankDetails: {
      accountName: '',
      accountNumber: '',
      routingNumber: '',
    },
    pickupDetails: {
      location: 'store_downtown',
      date: '',
      time: '',
    },
    shippingAddress: {
      address: '',
      city: '',
      state: '',
      zipCode: '',
    },
    newSizes: {},
    defectPhotos: [],
    isRejected: false,
    isProcessing: false,
    isCheckingReturns: true, // Start with checking state
    processingDuration: Math.floor(Math.random() * 5000) + 5000, // Random duration between 5-10 seconds
    refundFee: 0,
    lineItemsProcessed: [],
    termsChecked: false,
    policyChecked: false,
    existingReturnStatus: null, // Track status of existing returns
  });

  // Log flow state for debugging
  log.debug('Flow state updated:', { flowState });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  // Helper to update flow state
  const updateFlowState = (updates: Partial<FlowState>) => {
    setFlowState((prev) => ({ ...prev, ...updates }));
  };

  // Calculate total amount
  const totalAmount = selectedItems.reduce(
    (sum, item) =>
      sum +
      Number(item.node.originalUnitPriceSet.shopMoney.amount) *
        item.node.quantity,
    0
  );

  // Check if the order is within the return period on component mount
  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    // Calculate return period based on order data
    calculateReturnPeriod(order, updateFlowState);

    // Get the order name without the # prefix
    const orderName = order.name.replace('#', '');

    // Get line item IDs
    const lineItemIds = selectedItems.map((item) => item.node.id);

    // Check for all existing returns in the database (rejected, pending, approved, etc.)
    const checkExistingReturns = async () => {
      try {
        // Build query string for line item IDs
        const queryParams = new URLSearchParams();
        queryParams.append('orderName', orderName);
        for (const id of lineItemIds) {
          queryParams.append('lineItemId', id);
        }

        // Fetch all existing returns from API
        const response = await fetch(
          `/api/check-existing-returns?${queryParams.toString()}`
        );
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to get existing returns');
        }

        if (data.existingReturns && data.existingReturns.length > 0) {
          // Get all line item IDs that have existing returns
          const processedItems = data.existingReturns.map(
            (item: { lineItemId: string }) => item.lineItemId
          );

          // Get the status of the first return (for UI display purposes)
          const firstReturnStatus = data.existingReturns[0]?.status || null;

          // Check if any returns are rejected
          const hasRejectedReturns = data.existingReturns.some(
            (item: { status: string }) => item.status === 'rejected'
          );

          if (processedItems.length > 0) {
            updateFlowState({
              lineItemsProcessed: processedItems,
              existingReturnStatus: firstReturnStatus,
              isRejected: hasRejectedReturns, // Set rejected state if any returns are rejected
            });
          }
        }
      } catch (error) {
        log.error('Error checking existing returns:', { error });
      } finally {
        // Set checking state to false when done, regardless of outcome
        updateFlowState({ isCheckingReturns: false });
      }
    };

    checkExistingReturns();
  }, [order, selectedItems]);

  // Handle next step
  const handleNext = async () => {
    // If we're on the last step, complete the flow
    if (flowState.step === 5) {
      handleComplete();
      return;
    }

    // Special logic for step 1 (reason selection)
    if (flowState.step === 1 && flowState.returnReason) {
      // Check if the reason is ineligible
      const isIneligible = [
        'different_image',
        'no_longer_needed',
        'customer_damage',
      ].includes(flowState.returnReason);

      if (isIneligible) {
        // Start the rejection flow when Next is clicked
        updateFlowState({
          isProcessing: true,
        });

        try {
          // Save the ineligible return reason to the database for each selected item
          const orderName = order.name.replace('#', '');
          const email = order.email;

          // Prepare items data for API
          const items = selectedItems.map((item) => ({
            id: item.node.id,
            title: item.node.title,
            variantTitle: item.node.variantTitle,
            sku: item.node.sku,
          }));

          // Save rejected return via API
          const response = await fetch('/api/rejected-returns', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              orderName,
              email,
              items,
              returnReason: flowState.returnReason || '',
            }),
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || 'Failed to save rejected return');
          }

          log.info('Saved rejected return for items', {
            orderName,
            returnReason: flowState.returnReason,
            itemCount: selectedItems.length,
          });
        } catch (error) {
          log.error('Error saving rejected return:', { error });
        }

        // Shows loading to user for random duration
        setTimeout(() => {
          updateFlowState({
            isProcessing: false,
            isRejected: true,
          });
        }, flowState.processingDuration);

        return;
      }
    }

    // Special logic for step 2 (accessories check)
    if (flowState.step === 2 && flowState.hasAccessories === false) {
      // If no accessories, don't proceed
      return;
    }

    // Special logic for step 3 when reason is defect or wrong_product
    // Insert exchange type selection step
    if (
      flowState.step === 3 &&
      (flowState.returnReason === 'defect' ||
        flowState.returnReason === 'wrong_product') &&
      !flowState.exchangeType
    ) {
      // Don't proceed if exchange type is not selected
      return;
    }

    if (flowState.step === 3 && flowState.exchangeType === 'exchange') {
      updateFlowState({ returnLabelOption: 'envelope' });
    }

    updateFlowState({ step: flowState.step + 1 });
  };

  // Handle back step
  const handleBack = () => {
    if (flowState.step > 1) {
      updateFlowState({ step: flowState.step - 1 });
    }
  };
  // Handle complete
  const handleComplete = async () => {
    setIsSubmitting(true);

    try {
      const orderName = order.name.replace('#', '');
      const email = order.email;

      // Prepare data to send to backend
      const returnData = {
        orderName,
        email,
        orderItems: selectedItems.map((item) => ({
          id: item.node.id,
          title: item.node.title,
          variantTitle: item.node.variantTitle,
          quantity: item.node.quantity,
          price: item.node.originalUnitPriceSet.shopMoney.amount,
          currency: item.node.originalUnitPriceSet.shopMoney.currencyCode,
          sku: item.node.sku,
        })),
        returnReason: flowState.returnReason,
        exchangeType: flowState.exchangeType,
        defectType: flowState.defectType,
        defectDetails: flowState.defectDetails,
        defectPhotos: flowState.defectPhotos,
        returnLabelOption: flowState.returnLabelOption,
        refundFee: flowState.refundFee,
        totalAmount: totalAmount,
        processed: true, // Mark as processed to prevent future returns
        isSpecialApproval: flowState.isSpecialApproval,
      };

      log.info('Sending return data to backend:', returnData);

      // Submit the return request to the API
      const response = await fetch('/api/submit-return', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(returnData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to submit return request');
      }

      if (!data.success) {
        throw new Error(data.message || 'Failed to submit return request');
      }

      // Update the return number from the API response
      updateFlowState({
        returnNumber: data.returnNumber || getReturnNumber(orderName),
      });

      setIsComplete(true);
    } catch (error) {
      log.error('Error submitting return request:', { error });
      alert(
        dictionary.return?.submission_error ||
          'There was an error submitting your return request. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render button content based on current state
  const renderButtonContent = () => {
    if (isSubmitting) {
      return (
        <div className="flex items-center">
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          {dictionary.return.processing || 'Processing...'}
        </div>
      );
    }

    if (flowState.step === 5) {
      return dictionary.return.submit_request || 'Submit Request';
    }

    return (
      <>
        {dictionary.return.next || 'Next'}{' '}
        <ArrowRight className="ml-2 h-4 w-4" />
      </>
    );
  };

  // Render step content
  const renderStepContent = () => {
    // Show loading state while checking for returns
    if (flowState.isCheckingReturns) {
      return <ProcessingState dictionary={dictionary} />;
    }

    // Show processing indicator for rejection flow
    if (flowState.isProcessing) {
      return <ProcessingState dictionary={dictionary} />;
    }

    // Show rejection message
    if (flowState.isRejected) {
      return (
        <RejectedState
          selectedItems={selectedItems}
          dictionary={dictionary}
          returnReason={flowState.returnReason}
        />
      );
    }

    // Render the appropriate step component based on current step
    switch (flowState.step) {
      case 1:
        return (
          <Step1ReasonSelection
            orderName={order.name.replace('#', '')}
            flowState={flowState}
            updateFlowState={updateFlowState}
            selectedItems={selectedItems}
            dictionary={dictionary}
            totalAmount={totalAmount}
          />
        );
      case 2:
        return (
          <Step2AccessoriesCheck
            orderName={order.name.replace('#', '')}
            flowState={flowState}
            updateFlowState={updateFlowState}
            selectedItems={selectedItems}
            dictionary={dictionary}
            totalAmount={totalAmount}
          />
        );

      case 3:
        return (
          <Step3ReturnDetails
            orderName={order.name.replace('#', '')}
            flowState={flowState}
            updateFlowState={updateFlowState}
            selectedItems={selectedItems}
            dictionary={dictionary}
            totalAmount={totalAmount}
          />
        );

      case 4:
        return (
          <Step4ReturnInstructions
            orderName={order.name.replace('#', '')}
            flowState={flowState}
            updateFlowState={updateFlowState}
            selectedItems={selectedItems}
            dictionary={dictionary}
            totalAmount={totalAmount}
          />
        );

      case 5:
        return (
          <Step5Review
            orderName={order.name.replace('#', '')}
            flowState={flowState}
            updateFlowState={updateFlowState}
            selectedItems={selectedItems}
            dictionary={dictionary}
            totalAmount={totalAmount}
          />
        );

      default:
        return null;
    }
  };

  // If the order is complete, show the confirmation screen
  if (isComplete) {
    return (
      <CompletedState
        selectedItems={selectedItems}
        dictionary={dictionary}
        flowState={flowState}
        onComplete={onComplete}
        orderName={order.name.replace('#', '')}
      />
    );
  }

  let cardTitle = dictionary.return.return_request || 'Return Request';
  if (flowState.isRejected) {
    cardTitle = dictionary.return.request_rejected || 'Return Request Rejected';
  } else if (isReturnNotAllowed(flowState, selectedItems)) {
    cardTitle = dictionary.return?.not_available || 'Return Not Available';
  }

  return (
    <Card className="mx-auto w-full max-w-3xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{cardTitle}</CardTitle>
          {!flowState.isRejected &&
            !flowState.isProcessing &&
            !flowState.isCheckingReturns &&
            !isReturnNotAllowed(flowState, selectedItems) && (
              <div className="text-muted-foreground text-sm">
                {(dictionary.return.step_of || 'Step {step} of 5').replace(
                  '{step}',
                  String(flowState.step)
                )}
              </div>
            )}
        </div>
        {!flowState.isRejected &&
          !flowState.isProcessing &&
          !flowState.isCheckingReturns &&
          !isReturnNotAllowed(flowState, selectedItems) && (
            <div className="mt-2 h-2 w-full rounded-full bg-gray-200">
              <div
                className="h-2 rounded-full bg-primary transition-all duration-300"
                style={{ width: `${(flowState.step / 5) * 100}%` }}
              />
            </div>
          )}
      </CardHeader>
      <CardContent>
        {isReturnNotAllowed(flowState, selectedItems) ? (
          <ReturnNotAllowedState
            selectedItems={selectedItems}
            order={order}
            dictionary={dictionary}
            flowState={flowState}
          />
        ) : (
          renderStepContent()
        )}
      </CardContent>
      {!flowState.isRejected &&
        !flowState.isProcessing &&
        !flowState.isCheckingReturns &&
        !isReturnNotAllowed(flowState, selectedItems) && (
          <CardFooter className="flex justify-between">
            {flowState.step > 1 ? (
              <Button
                variant="outline"
                className="cursor-pointer"
                onClick={handleBack}
                disabled={isSubmitting}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> {dictionary.button.back}
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={() => {
                  // Call the onCancel callback to navigate back
                  onCancel();
                }}
                className="cursor-pointer"
                disabled={isSubmitting}
              >
                {dictionary.button.cancel}
              </Button>
            )}

            <Button
              onClick={handleNext}
              disabled={!isStepValid(flowState, selectedItems) || isSubmitting}
              className="min-w-[100px]"
            >
              {renderButtonContent()}
            </Button>
          </CardFooter>
        )}
      {(flowState.isRejected || isReturnNotAllowed(flowState, selectedItems)) &&
        !flowState.isCheckingReturns && (
          <CardFooter>
            <Button onClick={onCancel} className="w-full">
              {dictionary.button.back || 'Return to Order'}
            </Button>
          </CardFooter>
        )}
    </Card>
  );
}

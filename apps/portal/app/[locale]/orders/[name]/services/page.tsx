import { env } from '@/env';
import { verifyOrderAccess } from '@/lib/shopify';
import { getDictionary } from '@repo/internationalization';
import { redirect } from 'next/navigation';
import ServiceSelectionClient from './page.client';

interface ServiceSelectionPageProps {
  params: Promise<{
    name: string;
    locale: string;
  }>;
  searchParams: Promise<{
    email?: string;
    success?: string;
  }>;
}

export default async function ServiceSelectionPage({
  params,
  searchParams,
}: ServiceSelectionPageProps) {
  const { name, locale } = await params;
  const { email, success } = await searchParams;

  const dictionary = await getDictionary(locale);

  // Verify order access with email if provided
  const order = await verifyOrderAccess(name, email);

  // If order not found or email verification failed
  if (!order) {
    // Redirect to home page with error message, including the locale
    redirect(
      `/${locale}?error=verification_failed&orderName=${encodeURIComponent(name)}`
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 text-center">
        <h1 className="font-bold text-3xl uppercase tracking-tight">
          {env.NEXT_PUBLIC_SITE_TITLE ?? dictionary.site.title}
        </h1>
      </div>
      <ServiceSelectionClient
        order={order}
        dictionary={dictionary}
        success={success}
      />
    </div>
  );
}

'use client';

import { CmsContent } from '@/components/cms-content';
import type { GetOrdersQuery } from '@/types/admin.generated';
import {
  Alert,
  AlertDescription,
} from '@repo/design-system/components/ui/alert';
import { Button } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { dateFormatter as format } from '@repo/design-system/lib/format';
import type { Dictionary } from '@repo/internationalization';
import { log } from '@repo/observability/log';
import {
  AlertCircle,
  ArrowLeft,
  CheckCircle,
  Package,
  Truck,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

interface DeliveryPageClientProps {
  order: NonNullable<GetOrdersQuery['orders']['edges'][0]['node']>;
  dictionary: Dictionary;
}

// Helper function to format date
const formatDate = (
  dateString: string | null | undefined,
  notAvailable = 'N/A',
  invalidDate = 'Invalid date'
): string => {
  if (!dateString) {
    return notAvailable;
  }
  try {
    return format(new Date(dateString), 'yyyy-MM-dd');
  } catch (e) {
    log.error('Error formatting date:', { e });
    return invalidDate;
  }
};

// Helper function to format date with time
const formatDateTime = (
  dateString: string | null | undefined,
  notAvailable = 'N/A',
  invalidDate = 'Invalid date'
): { date: string; time: string } => {
  if (!dateString) {
    return { date: notAvailable, time: '' };
  }
  try {
    const date = new Date(dateString);
    return {
      date: format(date, 'yyyy-MM-dd'),
      time: format(date, 'HH:mm'),
    };
  } catch (e) {
    log.error('Error formatting date:', { e });
    return { date: invalidDate, time: '' };
  }
};

export default function DeliveryPageClient({
  order,
  dictionary,
}: DeliveryPageClientProps) {
  const router = useRouter();
  const { locale } = useParams();

  const handleBack = () => {
    router.push(
      `/orders/${order.name.replace('#', '')}/services?email=${encodeURIComponent(
        order.email || ''
      )}`
    );
  };

  // Get the latest fulfillment (if any)
  const latestFulfillment =
    order.fulfillments && order.fulfillments.length > 0
      ? order.fulfillments[0]
      : null;

  // Determine delivery status
  const getDeliveryStatus = () => {
    if (!latestFulfillment) {
      return 'pending';
    }

    if (latestFulfillment.deliveredAt) {
      return 'delivered';
    }
    if (latestFulfillment.inTransitAt) {
      return 'in_transit';
    }

    // Convert status to lowercase for easier comparison
    const status = latestFulfillment.status.toLowerCase();

    if (status.includes('success')) {
      return 'delivered';
    }
    if (status.includes('deliver')) {
      return 'delivered';
    }
    if (status.includes('transit') || status.includes('out_for_delivery')) {
      return 'in_transit';
    }
    if (status.includes('attempt')) {
      return 'attempted';
    }
    if (status.includes('fail')) {
      return 'failed';
    }
    if (status.includes('cancel')) {
      return 'cancelled';
    }

    // Default to processing for any other status
    return 'processing';
  };

  const deliveryStatus = getDeliveryStatus();

  // Get tracking info
  const trackingInfo =
    latestFulfillment?.trackingInfo && latestFulfillment.trackingInfo.length > 0
      ? latestFulfillment.trackingInfo[0]
      : null;

  // Get fulfillment events
  const events =
    latestFulfillment?.events?.edges?.map((edge: any) => edge.node) || [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="h-6 w-6 text-green-500" />;
      case 'in_transit':
        return <Truck className="h-6 w-6 text-blue-500" />;
      case 'attempted':
        return <AlertCircle className="h-6 w-6 text-yellow-500" />;
      case 'failed':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      case 'cancelled':
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      default:
        return <Package className="h-6 w-6 text-muted-foreground" />;
    }
  };

  // Get status display text
  const getStatusDisplayText = (status: string): string => {
    switch (status) {
      case 'delivered':
        return dictionary.delivery?.delivered || 'Delivered';
      case 'in_transit':
        return dictionary.delivery?.in_transit || 'In Transit';
      case 'attempted':
        return dictionary.delivery?.attempted || 'Delivery Attempted';
      case 'failed':
        return dictionary.delivery?.failed || 'Delivery Failed';
      case 'processing':
        return dictionary.delivery?.processing || 'Processing';
      case 'cancelled':
        return dictionary.delivery?.cancelled || 'Cancelled';
      default:
        return dictionary.delivery?.pending || 'Pending';
    }
  };

  // Format estimated delivery date
  const estimatedDelivery = latestFulfillment?.estimatedDeliveryAt
    ? formatDate(
        latestFulfillment.estimatedDeliveryAt,
        dictionary.delivery?.not_available || 'N/A',
        dictionary.delivery?.invalid_date || 'Invalid date'
      )
    : dictionary.delivery?.not_available || 'N/A';

  return (
    <div className="mx-auto max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.return.delivery_confirmation}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Optional CMS note */}
          <CmsContent
            contentKey="delivery_note"
            locale={locale as string}
            fallback=""
          />

          {!latestFulfillment && (
            <Alert>
              <AlertDescription>
                {dictionary.delivery?.no_information ||
                  'No delivery information is available for this order yet.'}
              </AlertDescription>
            </Alert>
          )}

          <div className="flex items-center gap-4 rounded-lg bg-muted p-4">
            {getStatusIcon(deliveryStatus)}
            <div>
              <p className="font-medium">
                {getStatusDisplayText(deliveryStatus)}
              </p>
              <p className="text-muted-foreground text-sm">
                {dictionary.delivery?.estimated || 'Estimated delivery'}:{' '}
                {estimatedDelivery}
              </p>
            </div>
          </div>

          {trackingInfo && (
            <div className="space-y-2">
              <h3 className="font-medium">
                {dictionary.delivery?.tracking || 'Tracking Information'}
              </h3>
              <p className="text-sm">
                {dictionary.delivery?.carrier || 'Carrier'}:{' '}
                {trackingInfo.company ||
                  dictionary.delivery?.not_available ||
                  'N/A'}
              </p>
              <p className="text-sm">
                {dictionary.delivery?.tracking_number || 'Tracking Number'}:{' '}
                {trackingInfo.number ||
                  dictionary.delivery?.not_available ||
                  'N/A'}
              </p>
            </div>
          )}

          {events.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-medium">
                {dictionary.delivery?.history || 'Delivery History'}
              </h3>
              <div className="space-y-4">
                {events.map((event: any) => {
                  const eventDateTime = formatDateTime(
                    event.happenedAt,
                    dictionary.delivery?.not_available || 'N/A',
                    dictionary.delivery?.invalid_date || 'Invalid date'
                  );
                  const location = [event.city, event.province, event.country]
                    .filter(Boolean)
                    .join(', ');

                  return (
                    <div
                      key={event.id}
                      className="border-primary/30 border-l-2 py-1 pl-4"
                    >
                      <p className="font-medium">{event.status}</p>
                      <p className="text-muted-foreground text-sm">
                        {eventDateTime.date} {eventDateTime.time}
                      </p>
                      {location && <p className="text-sm">{location}</p>}
                      {event.message && (
                        <p className="text-sm">{event.message}</p>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          <div className="mt-8 flex justify-between space-x-4 text-center">
            <Button
              variant="outline"
              onClick={handleBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {dictionary.button.back}
            </Button>

            {trackingInfo?.url && (
              <Button type="button" asChild>
                <a
                  href={trackingInfo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Truck className="h-4 w-4" />
                  {dictionary.delivery?.track_package || 'Track Package'}
                </a>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import { env } from '@/env';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { getDictionary } from '@repo/internationalization';
import Image from 'next/image';
import { Suspense } from 'react';
import logo from '../public/casefinite.png';
import OrderLookupForm from './page.client';

export default async function Home({
  params,
}: { params: Promise<{ locale: string }> }) {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mx-auto mb-10 flex max-w-3xl flex-col items-center justify-center text-center">
        <Image src={logo} alt={env.NEXT_PUBLIC_SITE_TITLE ?? 'Casefinite'} />
        <p className="text-muted-foreground text-xl">
          {dictionary.site.description}
        </p>
      </div>

      <div className="mx-auto max-w-2xl">
        <Suspense
          fallback={
            <Card>
              <CardHeader>
                <CardTitle>{dictionary.form.order}</CardTitle>
                <CardDescription>{dictionary.form.email}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex h-40 items-center justify-center">
                  <p className="text-muted-foreground">
                    {dictionary.message.loading}
                  </p>
                </div>
              </CardContent>
            </Card>
          }
        >
          <OrderLookupForm dictionary={dictionary} />
        </Suspense>
      </div>
    </div>
  );
}

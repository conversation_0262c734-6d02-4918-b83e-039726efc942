'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@repo/design-system/components/ui/accordion';
import { useParams } from 'next/navigation';
import { CmsContent } from './cms-content';

interface HowToContentProps {
  contentType: 'exchange' | 'return' | 'refund';
  className?: string;
}

/**
 * Component to display "How To" content from CMS
 * Used for "How to Exchange", "How to Return", and "About Refund" sections
 */
export function HowToContent({
  contentType,
  className = '',
}: HowToContentProps) {
  const { locale } = useParams();

  const contentKey =
    contentType === 'exchange'
      ? 'how_to_exchange'
      : contentType === 'return'
        ? 'how_to_return'
        : 'about_refund';

  const title =
    contentType === 'exchange'
      ? 'How to Exchange'
      : contentType === 'return'
        ? 'How to Return'
        : 'About Refund';

  return (
    <Accordion type="single" collapsible className={className}>
      <AccordionItem value="content">
        <AccordionTrigger>{title}</AccordionTrigger>
        <AccordionContent>
          <CmsContent
            contentKey={contentKey}
            locale={locale as string}
            fallback={`No information available about ${title.toLowerCase()}.`}
          />
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}

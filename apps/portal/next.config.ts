import path from 'node:path';
import { env } from '@/env';
import { withToolbar } from '@repo/feature-flags/lib/toolbar';
import { config, withAnalyzer } from '@repo/next-config';
import { withLogging, withSentry } from '@repo/observability/next-config';
import type { NextConfig } from 'next';

let nextConfig: NextConfig = withToolbar(withLogging(config));

// Store the original webpack configuration function (if it exists)
const originalWebpack = nextConfig.webpack || ((config) => config);

// Extend the webpack configuration
nextConfig.webpack = (config, options) => {
  // First apply the original webpack configuration
  const updatedConfig = originalWebpack(config, options);

  // Then add your custom modifications
  updatedConfig.resolve.alias = {
    ...updatedConfig.resolve.alias,
    html2canvas: path.resolve(__dirname, 'node_modules/html2canvas-pro'),
  };

  return updatedConfig;
};

nextConfig.experimental = {
  ...nextConfig.experimental, // Fix: was "expirimental"
  turbo: {
    resolveAlias: {
      html2canvas: 'html2canvas-pro',
    },
  },
};

if (env.VERCEL) {
  nextConfig = withSentry(nextConfig);
}

if (env.ANALYZE === 'true') {
  nextConfig = withAnalyzer(nextConfig);
}

export default nextConfig;

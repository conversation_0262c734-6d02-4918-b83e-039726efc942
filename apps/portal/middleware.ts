import { env } from '@/env';
import { internationalizationMiddleware } from '@repo/internationalization/middleware';
import {
  noseconeMiddleware,
  noseconeOptions,
  noseconeOptionsWithToolbar,
} from '@repo/security/middleware';
import type { NextRequest } from 'next/server';

export const config = {
  // matcher tells Next.js which routes to run the middleware on. This runs the
  // middleware on all routes except for API routes, static assets and Posthog ingest
  matcher: ['/((?!api|_next/static|_next/image|ingest|favicon.ico).*)'],
};

// Security headers middleware
const securityHeaders = env.FLAGS_SECRET
  ? noseconeMiddleware(noseconeOptionsWithToolbar)
  : noseconeMiddleware(noseconeOptions);

/**
 * Middleware function that chains organization resolution, internationalization, and security headers
 * This is a public portal, so we don't need authentication
 */
const middleware = (request: NextRequest) => {
  // Check if the request is trying to access the sign-in page
  // if (request.nextUrl.pathname.startsWith('/sign-in')) {
  //   // Redirect to the home page instead since this is a public portal
  //   return NextResponse.redirect(new URL('/', request.url));
  // }

  // Skip internationalization for API routes
  if (request.nextUrl.pathname.startsWith('/api')) {
    // Just apply security headers for API routes
    return securityHeaders();
  }

  // Handle internationalization for non-API routes
  const i18nResponse = internationalizationMiddleware(request);
  if (i18nResponse) {
    return i18nResponse;
  }

  // Finally, apply security headers
  return securityHeaders();
};

export default middleware;

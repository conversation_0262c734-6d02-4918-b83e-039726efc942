'use client';

import { signOut } from '@repo/auth/client';
import { UserButton } from '@repo/auth/components/user-button';
import type { User } from '@repo/database/types';
import {
  AnchorIcon,
  FileTextIcon,
  LifeBuoyIcon,
  ReceiptTextIcon,
  SendIcon,
  UsersRoundIcon,
} from '@repo/design-system/components/icons';
import { ModeToggle } from '@repo/design-system/components/mode-toggle';
import { Collapsible } from '@repo/design-system/components/ui/collapsible';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from '@repo/design-system/components/ui/sidebar';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import type { ReactNode } from 'react';
type GlobalSidebarProperties = {
  user?: User;
  readonly children: ReactNode;
};

const data = {
  user: {
    name: 'shadcn',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },
  navMain: [
    {
      title: 'Return Requests',
      url: '/return-requests',
      icon: AnchorIcon,
      isActive: true,
    },
    {
      title: 'Address Changes',
      url: '/address-changes',
      icon: SendIcon,
    },
    {
      title: 'Receipt Customizations',
      url: '/receipt-customizations',
      icon: ReceiptTextIcon,
    },
  ],
  navAdmin: [
    {
      title: 'Users',
      url: '/admin/users',
      icon: UsersRoundIcon,
    },
    {
      title: 'CMS Content',
      url: '/admin/cms',
      icon: FileTextIcon,
    },
  ],
  navSecondary: [
    // {
    //   title: 'Settings',
    //   url: '/settings',
    //   icon: Settings2Icon,
    // },
  ],
};

export const GlobalSidebar = ({ user, children }: GlobalSidebarProperties) => {
  useSidebar(); // Required for sidebar context
  const router = useRouter();
  const pathname = usePathname();

  return (
    <>
      <Sidebar variant="inset">
        <SidebarHeader>{/* Header content if needed */}</SidebarHeader>
        <SidebarContent>
          {user?.role === 'super-admin' && (
            <SidebarGroup>
              <SidebarGroupLabel>Platform</SidebarGroupLabel>
              <SidebarMenu>
                {data.navAdmin.map((item) => (
                  <Collapsible
                    key={item.title}
                    asChild
                    defaultOpen={pathname.startsWith(item.url)}
                  >
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        asChild
                        tooltip={item.title}
                        isActive={pathname === item.url}
                      >
                        <Link href={item.url}>
                          <item.icon />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </Collapsible>
                ))}
              </SidebarMenu>
            </SidebarGroup>
          )}
          <SidebarGroup>
            <SidebarGroupLabel>System</SidebarGroupLabel>
            <SidebarMenu>
              {data.navMain.map((item) => (
                <Collapsible
                  key={item.title}
                  asChild
                  defaultOpen={item.isActive}
                >
                  <SidebarMenuItem>
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      isActive={pathname === item.url}
                    >
                      <Link href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </Collapsible>
              ))}
            </SidebarMenu>
          </SidebarGroup>
          {/* <SidebarGroup className="mt-auto">
            <SidebarGroupContent>
              <SidebarMenu>
                {data.navSecondary.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild isActive={pathname === item.url}>
                      <Link href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup> */}
        </SidebarContent>
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem className="flex items-center gap-2">
              <UserButton
                user={user}
                showName
                onSignOut={() => {
                  signOut({
                    fetchOptions: {
                      onSuccess: () => {
                        router.push('/sign-in');
                      },
                    },
                  });
                }}
                className="flex-1"
              />
              <div className="flex shrink-0 items-center gap-px">
                <ModeToggle />
                {/* <Button
                  variant="ghost"
                  size="icon"
                  className="shrink-0"
                  asChild
                >
                  <div className="h-4 w-4">
                    <NotificationsTrigger />
                  </div>
                </Button> */}
              </div>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
      <SidebarInset>{children}</SidebarInset>
    </>
  );
};

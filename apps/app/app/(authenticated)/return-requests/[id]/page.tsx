import { Header } from '@/app/(authenticated)/components/header';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/design-system/components/ui/table';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON>bs<PERSON>ist,
  TabsTrigger,
} from '@repo/design-system/components/ui/tabs';
import { formatDateTime } from '@repo/design-system/lib/format';
import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { getReturnRequest } from '../actions';
import { BarcodeScannerTab } from './barcode-scanner-tab';
import { ReturnRequestImageDialog } from './page.client';
import { ReturnRequestStatusForm } from './return-request-status-form';

type PageProps = {
  readonly params: Promise<{
    id: string;
  }>;
};

export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { id } = await params;
  const returnRequest = await getReturnRequest(id);

  const title = 'Senders Return - Return Request';
  const description = `Details for Return #${returnRequest.returnNumber}`;

  return {
    title,
    description,
  };
}

export default async function ReturnRequestDetailPage({
  params,
}: PageProps): Promise<ReactElement> {
  const { id } = await params;
  const returnRequest = await getReturnRequest(id);

  return (
    <>
      <Header pages={['Return Requests']} page={returnRequest.returnNumber} />

      <main className="flex-1 space-y-4 p-4 pt-6 lg:p-8">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="font-bold text-xl tracking-tight">
            Return #{returnRequest.returnNumber}
          </h2>
          <ReturnRequestStatusForm
            id={returnRequest.id}
            currentStatus={returnRequest.status}
            processed={returnRequest.processed}
          />
        </div>
        <Tabs defaultValue="summary">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="items">
                Items ({returnRequest.returnItems.length})
              </TabsTrigger>
              <TabsTrigger value="scanner">Barcode Scanner</TabsTrigger>
              {returnRequest.defectPhotos.length > 0 && (
                <TabsTrigger value="photos">
                  Photos ({returnRequest.defectPhotos.length})
                </TabsTrigger>
              )}
            </TabsList>
          </div>
          <TabsContent value="summary" className="mt-6">
            <div className="grid gap-4 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Return Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Order</span>
                    <span>{returnRequest.orderName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">
                      Customer Email
                    </span>
                    <span>{returnRequest.email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Return Reason</span>
                    <span className="capitalize">
                      {returnRequest.returnReason.replace(/_/g, ' ')}
                    </span>
                  </div>
                  {returnRequest.exchangeType && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Exchange Type
                      </span>
                      <span>{returnRequest.exchangeType}</span>
                    </div>
                  )}
                  {returnRequest.defectType && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Defect Type</span>
                      <span>{returnRequest.defectType}</span>
                    </div>
                  )}
                  {returnRequest.defectDetails && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Defect Details
                      </span>
                      <span>{returnRequest.defectDetails}</span>
                    </div>
                  )}
                  {returnRequest.returnLabelOption && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Return Label Option
                      </span>
                      <span>{returnRequest.returnLabelOption}</span>
                    </div>
                  )}
                  {returnRequest.refundFee !== null && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Refund Fee</span>
                      <span>${returnRequest.refundFee.toFixed(2)}</span>
                    </div>
                  )}
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Status Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status</span>
                    <span className="capitalize">{returnRequest.status}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Processed</span>
                    <span>{returnRequest.processed ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Created</span>
                    <span>{formatDateTime(returnRequest.createdAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Updated</span>
                    <span>{formatDateTime(returnRequest.updatedAt)}</span>
                  </div>
                  {returnRequest.adminNotes && (
                    <div className="mt-4">
                      <span className="text-muted-foreground">Admin Notes</span>
                      <p className="mt-1 whitespace-pre-wrap">
                        {returnRequest.adminNotes}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="items" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Return Items</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Item</TableHead>
                      <TableHead>Variant</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Return Reason</TableHead>
                      <TableHead>Exchange Variant</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {returnRequest.returnItems.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.title}</TableCell>
                        <TableCell>{item.variantTitle || '-'}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>
                          {item.price.toFixed(2)} {item.currency}
                        </TableCell>
                        <TableCell>{item.returnReason || '-'}</TableCell>
                        <TableCell>{item.exchangeVariant || '-'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="scanner" className="mt-6">
            <BarcodeScannerTab returnRequest={returnRequest} />
          </TabsContent>
          {returnRequest.defectPhotos.length > 0 && (
            <TabsContent value="photos" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Defect Photos</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
                    {returnRequest.defectPhotos.map((photo) => (
                      <ReturnRequestImageDialog
                        key={photo.id}
                        photo={photo.url}
                      />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </main>
    </>
  );
}

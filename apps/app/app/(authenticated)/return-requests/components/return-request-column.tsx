'use client';

import { Badge } from '@repo/design-system/components/ui/badge';
import { formatDate } from '@repo/design-system/lib/format';
import type { ColumnDef } from '@tanstack/react-table';

// Define the ReturnRequest type based on what we're selecting in the action
export type ReturnRequestRow = {
  id: string;
  orderName: string;
  email: string;
  returnNumber: string;
  returnReason: string;
  exchangeType: string | null;
  status: string;
  processed: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    returnItems: number;
    defectPhotos: number;
  };
};

export const columns: ColumnDef<ReturnRequestRow>[] = [
  {
    accessorKey: 'returnNumber',
    header: 'Return #',
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue('returnNumber')}</div>
    ),
  },
  {
    accessorKey: 'orderName',
    header: 'Order',
  },
  {
    accessorKey: 'email',
    header: 'Customer Email',
  },
  {
    accessorKey: 'returnReason',
    header: 'Reason',
    cell: ({ row }) => {
      const reason = row.getValue('returnReason') as string;
      return <div className="capitalize">{reason.replace(/_/g, ' ')}</div>;
    },
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;

      return (
        <Badge
          variant={
            status === 'completed'
              ? 'default'
              : status === 'approved'
                ? 'success'
                : status === 'rejected'
                  ? 'destructive'
                  : 'secondary'
          }
        >
          {status}
        </Badge>
      );
    },
  },
  {
    accessorKey: 'processed',
    header: 'Processed',
    cell: ({ row }) => {
      const processed = row.getValue('processed') as boolean;

      return (
        <Badge variant={processed ? 'default' : 'outline'}>
          {processed ? 'Yes' : 'No'}
        </Badge>
      );
    },
  },
  {
    accessorKey: '_count.returnItems',
    header: 'Items',
    cell: ({ row }) => {
      const count = row.original._count.returnItems;
      return <div className="text-center">{count}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Created',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return <div>{formatDate(date)}</div>;
    },
  },
];

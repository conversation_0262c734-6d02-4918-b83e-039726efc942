import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getReturnRequests } from './actions';
import { columns } from './components/return-request-column';
import { ReturnRequestTable } from './components/return-request-table';

const title = 'Senders Return - Return Requests';
const description = 'Manage customer return and exchange requests';

export const metadata: Metadata = {
  title,
  description,
};

const ReturnRequestsPage = async (): Promise<ReactElement> => {
  const returnRequests = await getReturnRequests();

  return (
    <>
      <Header page="Return Requests" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <ReturnRequestTable columns={columns} initialData={returnRequests} />
        </div>
      </div>
    </>
  );
};

export default ReturnRequestsPage;

import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../components/header';
import { getAddressChangeRequests } from './actions';
import { columns } from './components/address-change-column';
import { AddressChangeTable } from './components/address-change-table';

const title = 'Senders Return - Address Changes';
const description = 'Manage customer address changes';

export const metadata: Metadata = {
  title,
  description,
};

const AddressChangeRequestsPage = async (): Promise<ReactElement> => {
  const addressChangeRequests = await getAddressChangeRequests();

  return (
    <>
      <Header page="Address Changes" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <AddressChangeTable
            columns={columns}
            initialData={addressChangeRequests}
          />
        </div>
      </div>
    </>
  );
};

export default AddressChangeRequestsPage;

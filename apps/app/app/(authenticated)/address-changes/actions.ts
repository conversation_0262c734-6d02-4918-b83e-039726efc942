'use server';

import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

// Get all address change requests
export async function getAddressChangeRequests() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const addressChangeRequests = await database.addressChangeRequest
    .findMany({
      select: {
        id: true,
        orderName: true,
        email: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })
    .then((addressChangeRequests) => serializePrisma(addressChangeRequests));

  return addressChangeRequests;
}

// Get a specific address change request by ID
export async function getAddressChangeRequest(id: string) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const addressChangeRequest = await database.addressChangeRequest.findUnique({
    where: { id },
  });

  if (!addressChangeRequest) {
    notFound();
  }

  return serializePrisma(addressChangeRequest);
}

// Update an address change request
export async function updateAddressChangeRequest(
  id: string,
  data: {
    status: string;
    adminNotes?: string;
  }
) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    throw new Error('Unauthorized');
  }

  const updatedAddressChangeRequest =
    await database.addressChangeRequest.update({
      where: { id },
      data,
    });

  revalidatePath('/address-changes');
  revalidatePath(`/address-changes/${id}`);

  return serializePrisma(updatedAddressChangeRequest);
}

'use client';

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import {
  Timeline,
  TimelineContent,
  TimelineDate,
  TimelineHeader,
  TimelineIndicator,
  TimelineItem,
  TimelineSeparator,
  TimelineTitle,
} from '@repo/design-system/components/ui/timeline';
import { dateFormatter } from '@repo/design-system/lib/format';
import { Calendar } from 'lucide-react';

interface TimelineEvent {
  id: string;
  type: 'created' | 'updated' | 'approved' | 'rejected' | 'completed';
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  status?: string;
}

interface TimelineProps {
  addressChange: {
    id: string;
    orderName: string;
    status: string;
    createdAt: string;
    updatedAt: string;
    adminNotes?: string;
  };
}

export function AddressTimeline({ addressChange }: TimelineProps) {
  // Generate timeline events based on address change data
  const generateTimelineEvents = (): TimelineEvent[] => {
    const events: TimelineEvent[] = [];

    // Created event
    events.push({
      id: 'created',
      type: 'created',
      title: 'Address Change Requested',
      description: `Customer requested address change for order ${addressChange.orderName}`,
      timestamp: new Date(addressChange.createdAt),
      user: 'Customer',
    });

    // Status change events (simulated based on current status)
    if (addressChange.status === 'approved') {
      events.push({
        id: 'approved',
        type: 'approved',
        title: 'Request Approved',
        description: 'Address change request has been approved by admin',
        timestamp: new Date(addressChange.updatedAt),
        user: 'Admin',
        status: 'approved',
      });
    } else if (addressChange.status === 'rejected') {
      events.push({
        id: 'rejected',
        type: 'rejected',
        title: 'Request Rejected',
        description:
          addressChange.adminNotes ||
          'Address change request has been rejected',
        timestamp: new Date(addressChange.updatedAt),
        user: 'Admin',
        status: 'rejected',
      });
    } else if (addressChange.status === 'completed') {
      events.push({
        id: 'approved',
        type: 'approved',
        title: 'Request Approved',
        description: 'Address change request has been approved by admin',
        timestamp: new Date(addressChange.updatedAt),
        user: 'Admin',
        status: 'approved',
      });
      events.push({
        id: 'completed',
        type: 'completed',
        title: 'Address Updated',
        description:
          'Shipping address has been successfully updated in the system',
        timestamp: new Date(addressChange.updatedAt),
        user: 'System',
        status: 'completed',
      });
    }

    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  };

  const events = generateTimelineEvents();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Request Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Timeline defaultValue={3}>
          {events.map((item, index) => (
            <TimelineItem
              key={item.id}
              step={index}
              className="group-data-[orientation=vertical]/timeline:sm:ms-32"
            >
              <TimelineHeader>
                <TimelineSeparator />
                <TimelineDate className="group-data-[orientation=vertical]/timeline:sm:-left-32 group-data-[orientation=vertical]/timeline:sm:absolute group-data-[orientation=vertical]/timeline:sm:w-20 group-data-[orientation=vertical]/timeline:sm:text-right">
                  {dateFormatter(item.timestamp, 'MMM d, yyyy h:mm a')}
                </TimelineDate>
                <TimelineTitle className="sm:-mt-0.5">
                  {item.title}
                </TimelineTitle>
                <TimelineIndicator />
              </TimelineHeader>
              <TimelineContent>{item.description}</TimelineContent>
            </TimelineItem>
          ))}
        </Timeline>
      </CardContent>
    </Card>
  );
}

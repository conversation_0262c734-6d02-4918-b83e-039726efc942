import { auth } from '@repo/auth/server';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import { Header } from './components/header';

const title = 'Senders Return';
const description = 'My application.';

export const metadata: Metadata = {
  title,
  description,
};

const App = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    notFound();
  }

  return (
    <>
      <Header page="Dashboard" />
      <div className="flex flex-1 flex-col gap-4 p-4 pt-0" />
    </>
  );
};

export default App;

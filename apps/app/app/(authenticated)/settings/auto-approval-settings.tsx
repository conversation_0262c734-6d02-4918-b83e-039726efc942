'use client';

import { But<PERSON> } from '@repo/design-system/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/design-system/components/ui/card';
import { Input } from '@repo/design-system/components/ui/input';
import { Label } from '@repo/design-system/components/ui/label';
import { useState, useEffect } from 'react';
import { getAutoApprovalDays, updateAutoApprovalDays } from './actions';

export function AutoApprovalSettings() {
  const [days, setDays] = useState<number>(30);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadAutoApprovalDays();
  }, []);

  const loadAutoApprovalDays = async () => {
    try {
      const currentDays = await getAutoApprovalDays();
      setDays(currentDays);
    } catch (error) {
      console.error('Failed to load auto-approval days:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDaysChange = (value: string) => {
    const numValue = parseInt(value, 10);
    if (!isNaN(numValue) && numValue >= 1 && numValue <= 365) {
      setDays(numValue);
      setHasChanges(true);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await updateAutoApprovalDays(days);
      setHasChanges(false);
      alert('Auto-approval settings saved successfully');
    } catch (error) {
      console.error('Failed to save auto-approval days:', error);
      alert('Failed to save auto-approval settings');
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    await loadAutoApprovalDays();
    setHasChanges(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Auto-Approval Settings</CardTitle>
        <CardDescription>
          Configure the number of days within which return requests are automatically approved.
          Requests outside this window will require manual review.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        ) : (
          <>
            <div className="space-y-2">
              <Label htmlFor="approval-days">
                Number of days allowed for auto-approval
              </Label>
              <Input
                id="approval-days"
                type="number"
                min="1"
                max="365"
                value={days}
                onChange={(e) => handleDaysChange(e.target.value)}
                className="w-32"
              />
              <p className="text-sm text-muted-foreground">
                Return requests made within {days} days of purchase will be automatically approved.
                Valid range: 1-365 days.
              </p>
            </div>

            {hasChanges && (
              <div className="flex gap-2 pt-4 border-t">
                <Button 
                  onClick={handleSave} 
                  disabled={isSaving}
                  className="flex items-center gap-2"
                >
                  {isSaving ? (
                    <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  ) : null}
                  Save Changes
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleReset}
                  disabled={isSaving}
                >
                  Reset
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}

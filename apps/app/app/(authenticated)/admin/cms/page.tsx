import type { Metadata } from 'next';
import type { ReactElement } from 'react';
import { Header } from '../../components/header';
import { getCmsContents } from './actions';
import { CmsContentTable } from './components/cms-content-table';
import { columns } from './components/columns';

const title = 'Senders Return - CMS Content';
const description = 'Manage dynamic content for the return portal';

export const metadata: Metadata = {
  title,
  description,
};

const CmsPage = async (): Promise<ReactElement> => {
  const content = await getCmsContents();

  return (
    <>
      <Header page="CMS Content" />

      <div className="flex flex-1 flex-col gap-4 px-4">
        <div className="min-h-[100vh] flex-1 md:min-h-min">
          <CmsContentTable columns={columns} initialData={content} />
        </div>
      </div>
    </>
  );
};

export default CmsPage;

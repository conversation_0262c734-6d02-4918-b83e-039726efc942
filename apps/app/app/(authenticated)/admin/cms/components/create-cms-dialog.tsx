'use client';

import { <PERSON><PERSON> } from '@repo/design-system/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@repo/design-system/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
  GridFormItem,
  useForm,
  zodResolver,
} from '@repo/design-system/components/ui/form';
import { Input } from '@repo/design-system/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/design-system/components/ui/select';
import { Switch } from '@repo/design-system/components/ui/switch';
import { Textarea } from '@repo/design-system/components/ui/textarea';
import { useToast } from '@repo/design-system/components/ui/use-toast';
import { locales } from '@repo/internationalization/index.client';
import { Plus, Save } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { z } from 'zod';
import { type ContentFormData, createOrUpdateContent } from '../actions';

// Define the form schema with Zod
const formSchema = z.object({
  id: z.string(),
  key: z.string().min(1, 'Content key is required'),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  isPublished: z.boolean(),
  content: z.record(z.string(), z.string()),
});

// Define the form type
type FormValues = z.infer<typeof formSchema>;

export function CreateCmsDialog() {
  const router = useRouter();
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [activeLocale, setActiveLocale] = useState(locales[0]);

  // Initialize form with react-hook-form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: '',
      key: '',
      title: '',
      description: '',
      isPublished: true,
      content: Object.fromEntries(locales.map((locale) => [locale, ''])),
    },
  });

  // Handle content changes for the active locale
  const handleContentChange = (value: string) => {
    const currentContent = form.getValues('content');
    form.setValue('content', {
      ...currentContent,
      [activeLocale]: value,
    });
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      await createOrUpdateContent(values as ContentFormData);

      toast({
        title: 'Content created',
        description: `Successfully created content: ${values.title}`,
      });

      // Reset form and close dialog
      form.reset({
        id: '',
        key: '',
        title: '',
        description: '',
        isPublished: true,
        content: Object.fromEntries(locales.map((locale) => [locale, ''])),
      });

      setOpen(false);
      router.refresh();
    } catch (error) {
      console.error('Error creating content:', error);
      toast({
        title: 'Error',
        description: 'Failed to create content. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Content
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create New CMS Content</DialogTitle>
          <DialogDescription>
            Add new dynamic content to display in the portal.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="key"
                  render={({ field }) => (
                    <GridFormItem className="space-y-2">
                      <FormLabel>Content Key</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select a key" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="delivery_note">
                            Delivery Note
                          </SelectItem>
                          <SelectItem value="how_to_return">
                            How to Return
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription className="text-xs">
                        Unique identifier used to retrieve this content
                      </FormDescription>
                      <FormMessage />
                    </GridFormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <GridFormItem className="space-y-2">
                      <FormLabel>Display Title</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Delivery Note" {...field} />
                      </FormControl>
                      <FormMessage />
                    </GridFormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <GridFormItem className="space-y-2">
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Brief description of this content"
                        rows={2}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isPublished"
                render={({ field }) => (
                  <GridFormItem className="space-y-2">
                    <div className="flex items-center justify-between">
                      <FormLabel>Published</FormLabel>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </div>
                    <FormDescription className="text-xs">
                      Only published content is visible on the portal
                    </FormDescription>
                    <FormMessage />
                  </GridFormItem>
                )}
              />

              <div className="space-y-2">
                <FormLabel>Content</FormLabel>
                <div className="mb-2 flex space-x-2">
                  {locales.map((locale) => (
                    <Button
                      key={locale}
                      type="button"
                      variant={activeLocale === locale ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setActiveLocale(locale)}
                    >
                      {locale.toUpperCase()}
                    </Button>
                  ))}
                </div>
                <Textarea
                  placeholder={`Content in ${activeLocale}`}
                  value={form.getValues().content[activeLocale] || ''}
                  onChange={(e) => handleContentChange(e.target.value)}
                  rows={6}
                />
                <p className="text-muted-foreground text-xs">
                  HTML formatting is supported
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">
                <Save className="mr-2 h-4 w-4" />
                Create Content
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

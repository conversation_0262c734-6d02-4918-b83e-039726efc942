import { auth } from '@repo/auth/server';
import { database, serializePrisma } from '@repo/database';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

export const getUsers = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const users = await database.user
    .findMany({
      select: {
        id: true,
        name: true,
        firstName: true,
        lastName: true,
        email: true,
        phone: true,
      },
    })
    .then((users) => serializePrisma(users));

  return users;
};

export const getUser = async (id: string) => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user) {
    return notFound();
  }

  const user = await database.user.findUnique({
    where: {
      id,
    },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      email: true,
      phone: true,
      dob: true,
      role: true,
    },
  });

  if (!user) {
    notFound();
  }

  return serializePrisma(user);
};

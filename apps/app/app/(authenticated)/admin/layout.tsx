import { auth } from '@repo/auth/server';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import type { ReactNode } from 'react';

type AdminLayoutProperties = {
  readonly children: ReactNode;
};

const AdminLayout = async ({ children }: AdminLayoutProperties) => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.role || session.user.role !== 'super-admin') {
    // Check if user is a super admin
    redirect('/');
  }

  return children;
};

export default AdminLayout;

import type {
  getAddressChangeRequest,
  getAddressChangeRequests,
} from './app/(authenticated)/address-changes/actions';
// Import actions from their respective locations
import type {
  getCmsContent,
  getCmsContents,
} from './app/(authenticated)/admin/cms/actions';
import type { getUsers } from './app/(authenticated)/admin/users/actions';
import type {
  getReceiptCustomization,
  getReceiptCustomizations,
} from './app/(authenticated)/receipt-customizations/actions';
import type {
  getReturnRequest,
  getReturnRequests,
} from './app/(authenticated)/return-requests/actions';

export type SerializedUsers = Awaited<ReturnType<typeof getUsers>>;
export type SerializedUser = SerializedUsers[number];

// Return Request types
export type SerializedReturnRequests = Awaited<
  ReturnType<typeof getReturnRequests>
>;
export type SerializedReturnRequest = Awaited<
  ReturnType<typeof getReturnRequest>
>;

// Address Change types
export type SerializedAddressChangeRequests = Awaited<
  ReturnType<typeof getAddressChangeRequests>
>;
export type SerializedAddressChangeRequest = Awaited<
  ReturnType<typeof getAddressChangeRequest>
>;

// Receipt Customization types
export type SerializedReceiptCustomizations = Awaited<
  ReturnType<typeof getReceiptCustomizations>
>;
export type SerializedReceiptCustomization = Awaited<
  ReturnType<typeof getReceiptCustomization>
>;

// CMS Content types
export type SerializedCmsContent = Awaited<ReturnType<typeof getCmsContent>>;
export type SerializedCmsContents = Awaited<ReturnType<typeof getCmsContents>>;

import { withCors } from '@/app/lib/api';
import { database, serializePrisma } from '@repo/database';
import { type NextRequest, NextResponse } from 'next/server';

const handler = async (request: NextRequest) => {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');

  const users = await database.user.findMany({
    where: {
      OR: [
        {
          firstName: {
            contains: query ?? '',
            mode: 'insensitive',
          },
        },
        {
          lastName: {
            contains: query ?? '',
            mode: 'insensitive',
          },
        },
        {
          email: {
            contains: query ?? '',
            mode: 'insensitive',
          },
        },
        {
          phone: {
            contains: query ?? '',
            mode: 'insensitive',
          },
        },
      ],
    },
    select: {
      id: true,
      name: true,
      firstName: true,
      lastName: true,
      email: true,
      phone: true,
    },
  });

  return NextResponse.json(
    { data: serializePrisma(users) },
    {
      status: 200,
    }
  );
};

export const OPTIONS = withCors(handler);
export const GET = withCors(handler);
